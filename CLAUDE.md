# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Code Rules
We coding everything as simple as possible, with as much less overwhelm as possible.
My name is <PERSON> and when you confirming with coding with KISS (Keep It As Simple As Possible) rule always tell me <PERSON>, i got you.

# App Goal
The final goal of the app is creating responder for Instagram messages with AI.

## Architecture Overview

This is a Next.js 15 monorepo (called "AIChromaPRO") using Turborepo for build orchestration and pnpm for package management. The codebase follows a modular architecture with three main applications and shared packages.

### Applications
- **Dashboard** (`apps/dashboard/`) - Main SaaS application at localhost:3000
- **Marketing** (`apps/marketing/`) - Marketing website at localhost:3001  
- **Public API** (`apps/public-api/`) - REST API at localhost:3002

### Shared Packages (`packages/`)
- `database` - Prisma ORM with PostgreSQL schema and migrations
- `auth` - Authentication using Auth.js with multi-org support
- `billing` - Stripe integration for subscriptions and payments
- `ui` - Shared React components with shadcn/ui
- `email` - Email templates and providers (Resend, Nodemailer, etc.)
- `analytics` - Analytics providers (Google Analytics, PostHog, Umami)
- `monitoring` - Error tracking with Sentry
- `api-keys` - API key generation and verification
- `webhooks` - Webhook handling utilities
- `common` - Shared utilities and types

## Development Commands

### Initial Setup
```bash                              
pnpm --filter database migrate dev        # Apply database migrations
```

### Development
```bash
pnpm dev                                  # Start all apps in parallel
pnpm --filter dashboard dev               # Start only dashboard
pnpm --filter marketing dev               # Start only marketing
pnpm --filter public-api dev              # Start only public-api
```

### Building & Testing
```bash
pnpm build                                # Build all packages
pnpm typecheck                            # Type checking across monorepo
pnpm lint                                 # Lint all packages
pnpm lint:fix                             # Fix linting issues
pnpm format                               # Check formatting
pnpm format:fix                           # Fix formatting
```

### Database Operations
```bash
pnpm --filter database generate           # Generate Prisma client
pnpm --filter database push               # Push schema changes without migration
pnpm --filter database studio             # Open Prisma Studio (port 3003)
pnpm --filter database migrate dev        # Create and apply migration
```

### Package Management
```bash
pnpm syncpack:list                        # Check for dependency mismatches
pnpm syncpack:fix                         # Fix dependency mismatches
pnpm clean                                # Clean root node_modules
pnpm clean:workspaces                     # Clean all workspace node_modules
```

## Key Technologies

- **Framework**: Next.js 15 with App Router and Turbopack
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: Auth.js with Google/Microsoft OAuth
- **UI**: React 19, shadcn/ui components, Tailwind CSS
- **Payments**: Stripe with customer portal
- **Email**: Multiple providers (Resend recommended)
- **Deployment**: Configured for modern hosting platforms

## Database Schema

The Prisma schema includes multi-organization support with:
- Users can belong to multiple organizations
- Role-based permissions (OWNER, ADMIN, MEMBER)
- Contact management with CRM features
- Billing integration with Stripe
- API key management
- Webhook configurations

## Environment Setup

Each app requires environment variables for:
- Database connection (`DATABASE_URL`)
- Auth providers (Google, Microsoft)
- Stripe billing configuration
- Email provider credentials
- Analytics and monitoring services

Refer to the `.env.example` files in each app for required variables.

## Testing & Quality

Run `pnpm lint` and `pnpm typecheck` before committing. The monorepo uses:
- ESLint with workspace-shared configuration
- Prettier for formatting
- TypeScript strict mode
- Turborepo caching for performance

## Date 
It's 2025 year