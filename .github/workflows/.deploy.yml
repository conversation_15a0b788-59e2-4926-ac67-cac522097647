name: Deploy to Server

on:
  push:
    branches: [ main ]  # Change to your default branch name if different

jobs:
  deploy:
    runs-on: ubuntu-latest
    timeout-minutes: 30  # Increase if your build takes longer
    
    steps:
    - name: Deploy to server
      uses: appleboy/ssh-action@v1.0.3
      with:
        host: ${{ secrets.HOST }}
        username: root
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        timeout: 1800s  # 30 minutes timeout for SSH connection
        command_timeout: 1800s  # 30 minutes timeout for each command
        script: |
          cd ai-setter/AIChromaPRO
          git pull
          cd ai-setter/AIChromaPRO/packages/database
          npx prisma migrate deploy
          echo "📥 deploying prisma migration..."
          npx prisma generate
          echo "📥 generating prisma..."
          cd
          echo "📥 cd..."
          cd ai-setter/AIChromaPRO/apps/dashboard
          echo "📥 Pulling latest changes..."
          git pull
          echo "🔨 Starting build process..."
          pnpm build
          echo "🔄 Restarting PM2 processes..."
          pm2 restart all
          echo "✅ Deployment completed successfully!"