# Repository Guidelines

## Project Structure & Module Organization
- apps/: Next.js apps (e.g., `apps/dashboard`, `apps/marketing`, `apps/public-api`).
- packages/: Shared libraries (e.g., `packages/ai`, `packages/billing`, `packages/ui`, `packages/database`).
- tooling/: Central configs (`tooling/eslint-config`, `tooling/prettier-config`, `tooling/typescript-config`).
- AIChromaPRO-chrome-extension/: Chrome extension code.

## Build, Test, and Development Commands
- Install: `pnpm i` (Node >= 20, PNPM 9). Preinstall runs requirements checks.
- Dev (all): `pnpm dev` (Turbo runs apps in parallel).
- Dev (one app): `pnpm -F dashboard dev` or `pnpm -F public-api dev`.
- Build (all): `pnpm build` or `pnpm run build:dashboard` for just Dashboard.
- Start: `pnpm start` (serve built apps) or `pnpm -F marketing start`.
- Lint/Format: `pnpm lint`, `pnpm format:fix`.
- Types: `pnpm typecheck`.
- Tests: `pnpm test` (Turbo) or `pnpm -F @workspace/ai test`.

## Coding Style & Naming Conventions
- Language: TypeScript first; Next.js/React in apps.
- Formatting: Prettier via `@workspace/prettier-config` (2 spaces, single quotes, ordered imports).
- Linting: ESLint flat config via `@workspace/eslint-config` (React/Next rules enabled in apps).
- Files: kebab-case for general files (`get-instagram-messages.ts`), PascalCase for React components.
- Exports: prefer named exports; default exports only for pages/components where idiomatic.

## Testing Guidelines
- Framework: Vitest (present in `packages/ai`).
- Location: colocate as `*.test.ts` near sources or under `__tests__/`.
- Scope: cover core logic and critical paths; mock external services.
- Run: `pnpm test` for workspace or filter by package with `-F`.

## Commit & Pull Request Guidelines
- Commits: short, imperative summaries (e.g., "Fix date/time prompt"). Conventional Commit prefixes are not enforced.
- PRs: include clear description, linked issues, steps to test, and screenshots for UI changes. Note `.env*` changes and migrations.

## Security & Configuration Tips
- Environment: use per-app `.env.local`. Required keys are listed in `turbo.json > globalEnv`.
- Secrets: never commit secrets; rotate compromised keys immediately.
- Data: Prisma/DB config lives in `packages/database`; create migrations in a separate PR when possible.

## Code Rules
We coding everything as simple as possible, with as much less overwhelm as possible.
My name is Alex and when you confirming with coding with KISS (Keep It As Simple As Possible) rule always tell me Alex, i got you.