-- CreateTable
CREATE TABLE "AIDollarsBalance" (
    "id" UUID NOT NULL DEFAULT gen_random_uuid(),
    "organizationId" UUID NOT NULL,
    "balance" DECIMAL(10,2) NOT NULL DEFAULT 0,
    "registeredAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "lastMonthlyChargeAt" TIMESTAMP(3),
    "lastTopupAt" TIMESTAMP(3),
    "nextMonthlyChargeAt" TIMESTAMP(3),
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "PK_AIDollarsBalance" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "AIDollarsBalance_organizationId_key" ON "AIDollarsBalance"("organizationId");

-- AddForeignKey
ALTER TABLE "AIDollarsBalance" ADD CONSTRAINT "AIDollarsBalance_organizationId_fkey" FOREIGN KEY ("organizationId") REFERENCES "Organization"("id") ON DELETE CASCADE ON UPDATE CASCADE;