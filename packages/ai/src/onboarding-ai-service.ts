/**
 * AI service for automated onboarding flow
 * Handles business analysis, bot style selection, and variable filling
 */

import type {
  AnthropicMessage,
  AnthropicRequest
} from './types';
import { OpenRouterClient } from './openrouter-client';
import { ZAIClient } from './zai-client';
import { AnthropicClient } from './anthropic-client';
import { onboardingTools } from './tools/onboarding-tools';
import { OnboardingToolExecutor } from './tools/onboarding-tool-executor';
import { GLM_MODELS, CLAUDE_MODELS } from './types';

export interface OnboardingConfig {
  apiKey?: string;
  zaiApiKey?: string;
  anthropicApiKey?: string;
  model: string;
  enableReasoning?: boolean;
  reasoningMaxTokens?: number;
  enableThinking?: boolean;
  cacheDuration?: '5m' | '1h';
  temperature?: number;
  topP?: number;
}

export interface OnboardingContext {
  organizationId: string;
  userId: string;
}

export class OnboardingAIService {
  private readonly openRouterClient: OpenRouterClient | null = null;
  private readonly zaiClient: ZAIClient | null = null;
  private readonly anthropicClient: AnthropicClient | null = null;
  private readonly toolExecutor: OnboardingToolExecutor;
  private readonly provider: 'openrouter' | 'zai' | 'anthropic';
  private readonly config: OnboardingConfig;

  constructor(config: OnboardingConfig) {
    this.config = config;
    this.toolExecutor = new OnboardingToolExecutor();

    // Determine provider based on model name
    if (this.isClaudeModel(config.model)) {
      this.provider = 'anthropic';
      if (!config.anthropicApiKey) {
        throw new Error('Anthropic API key is required for Claude models');
      }
      this.anthropicClient = new AnthropicClient({
        apiKey: config.anthropicApiKey,
        model: config.model,
        enableThinking: config.enableThinking,
        cacheDuration: config.cacheDuration,
        temperature: config.temperature,
        topP: config.topP
      });
    } else if (this.isGLMModel(config.model)) {
      this.provider = 'zai';
      if (!config.zaiApiKey) {
        throw new Error('Z.AI API key is required for GLM models');
      }
      this.zaiClient = new ZAIClient({
        apiKey: config.zaiApiKey,
        model: config.model,
        enableReasoning: config.enableReasoning,
        reasoningMaxTokens: config.reasoningMaxTokens
      });
    } else {
      this.provider = 'openrouter';
      if (!config.apiKey) {
        throw new Error('OpenRouter API key is required for OpenRouter models');
      }
      this.openRouterClient = new OpenRouterClient({
        apiKey: config.apiKey,
        model: config.model,
        enableReasoning: config.enableReasoning,
        reasoningMaxTokens: config.reasoningMaxTokens,
        temperature: config.temperature,
        topP: config.topP
      });
    }
  }

  private isGLMModel(model: string): boolean {
    return GLM_MODELS.includes(model as any);
  }

  private isClaudeModel(model: string): boolean {
    return CLAUDE_MODELS.includes(model as any);
  }

  /**
   * Create system message for onboarding flow
   */
  createOnboardingSystemMessage(): AnthropicMessage {
    const systemPrompt = `You are an AI onboarding assistant for an Instagram automation platform. Your goal is to help new users set up their Instagram automation by:

1. **Business Analysis**: Get their Instagram handle and analyze their business using web search
2. **Bot Style Selection**: Choose the best matching bot style from available options
3. **Variable Configuration**: Fill in all required variables based on gathered information
4. **Confirmation**: Present the configuration for user approval

## Available Tools:
- \`web_search\`: Search for information about businesses and Instagram profiles
- \`get_bot_styles\`: Get all available bot styles to choose from
- \`get_bot_style_variables\`: Get variables for a selected bot style
- \`save_onboarding_config\`: Save the final configuration

## Process Flow:
1. **Start**: Ask for Instagram handle if not provided
2. **Analyze**: Use web_search to gather business information
3. **Select**: Use get_bot_styles and choose the best match
4. **Configure**: Use get_bot_style_variables and fill them based on analysis
5. **Confirm**: Present configuration and ask for user confirmation
6. **Save**: Use save_onboarding_config to finalize setup

## Guidelines:
- Be conversational and friendly
- Explain your analysis and reasoning
- Ask for clarification when information is unclear
- Present information clearly and concisely
- Always confirm before making final changes

Start by greeting the user and asking for their Instagram handle.`;

    return {
      role: 'system',
      content: systemPrompt
    };
  }

  /**
   * Build request for Anthropic (Claude) models
   */
  buildAnthropicRequest(
    systemMessage: AnthropicMessage,
    conversationMessages: AnthropicMessage[],
    config: OnboardingConfig
  ): AnthropicRequest {
    return {
      model: config.model,
      messages: [systemMessage, ...conversationMessages],
      max_tokens: 4096,
      temperature: config.temperature || 1.0,
      top_p: config.topP || 1.0,
      tools: onboardingTools.map(tool => ({
        name: tool.name,
        description: tool.description,
        input_schema: tool.parameters
      })),
      ...(config.enableThinking && {
        thinking: { enabled: true }
      })
    };
  }

  /**
   * Process onboarding conversation with tool support
   */
  async processConversation(
    messages: { role: 'user' | 'assistant'; content: string }[],
    context: OnboardingContext
  ): Promise<{
    response: string;
    toolCalls?: any[];
    reasoning?: string;
  }> {
    console.log(`🤖 Processing onboarding conversation using ${this.provider.toUpperCase()}`);

    try {
      const systemMessage = this.createOnboardingSystemMessage();

      // Convert messages to appropriate format
      const conversationMessages: AnthropicMessage[] = messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      if (this.provider === 'anthropic' && this.anthropicClient) {
        const request = this.buildAnthropicRequest(systemMessage, conversationMessages, this.config);

        // Use the anthropic client's generateResponse method, but we need to adapt it
        // For now, we'll implement a simplified version
        const response = await this.processWithAnthropicTools(request, context);
        return response;

      } else if (this.provider === 'zai' && this.zaiClient) {
        // TODO: Implement ZAI support with tools
        throw new Error('ZAI tool support not yet implemented for onboarding');

      } else if (this.provider === 'openrouter' && this.openRouterClient) {
        // TODO: Implement OpenRouter support with tools
        throw new Error('OpenRouter tool support not yet implemented for onboarding');

      } else {
        throw new Error('No valid AI client configured');
      }

    } catch (error) {
      console.error('❌ Error processing onboarding conversation:', error);
      throw error;
    }
  }

  /**
   * Process conversation with Anthropic and handle tools
   * Uses the existing AnthropicClient's generateResponse method with custom tool handling
   */
  private async processWithAnthropicTools(
    request: AnthropicRequest,
    context: OnboardingContext
  ): Promise<{
    response: string;
    toolCalls?: any[];
    reasoning?: string;
  }> {
    if (!this.anthropicClient) {
      throw new Error('Anthropic client not initialized');
    }

    try {
      // For onboarding, we need to handle tools manually since the existing generateResponse
      // is designed for Instagram responses. We'll use a simplified approach.

      // Build parameters for the Anthropic client
      const anthropicParams = this.anthropicClient.buildAnthropicParams(request);

      // Import Anthropic SDK directly for this specific use case
      const Anthropic = (await import('@anthropic-ai/sdk')).default;
      const client = new Anthropic({
        apiKey: this.config.anthropicApiKey!,
      });

      // Make the API call
      const response = await client.messages.create(anthropicParams) as any;

      // Type guard to ensure we have a Message response, not a Stream
      if (!response.content || typeof response.content === 'string') {
        throw new Error('Invalid response format from Anthropic API');
      }

      // Check if Claude wants to use tools
      const toolUseBlocks = response.content.filter((block: any) => block.type === 'tool_use');
      const textBlocks = response.content.filter((block: any) => block.type === 'text');

      if (toolUseBlocks.length > 0) {
        console.log(`🔧 Claude wants to use ${toolUseBlocks.length} tools`);

        // Execute tools
        const toolResults = [];
        for (const toolBlock of toolUseBlocks) {
          try {
            const result = await this.toolExecutor.execute(
              toolBlock.name,
              toolBlock.input,
              context.organizationId
            );
            toolResults.push({
              type: 'tool_result' as const,
              tool_use_id: toolBlock.id,
              content: result
            });
          } catch (error) {
            console.error(`❌ Tool execution failed:`, error);
            toolResults.push({
              type: 'tool_result' as const,
              tool_use_id: toolBlock.id,
              content: `Error: ${error instanceof Error ? error.message : 'Tool execution failed'}`
            });
          }
        }

        // Continue conversation with tool results
        const updatedMessages = [
          ...anthropicParams.messages,
          { role: 'assistant' as const, content: response.content },
          { role: 'user' as const, content: toolResults }
        ];

        // Get final response after tool execution
        const finalResponse = await client.messages.create({
          ...anthropicParams,
          messages: updatedMessages
        }) as any;

        const finalTextBlock = finalResponse.content?.find((block: any) => block.type === 'text');

        return {
          response: finalTextBlock?.text || 'No response generated',
          toolCalls: toolUseBlocks.map((block: any) => ({
            name: block.name,
            parameters: block.input
          })),
          reasoning: undefined // Claude thinking not exposed via API
        };
      }

      // No tools used, return direct response
      const textBlock = textBlocks[0];
      return {
        response: textBlock?.text || 'No response generated',
        reasoning: undefined
      };

    } catch (error) {
      console.error('❌ Error in processWithAnthropicTools:', error);
      throw error;
    }
  }
}