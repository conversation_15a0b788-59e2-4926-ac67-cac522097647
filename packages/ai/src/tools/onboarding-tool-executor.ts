/**
 * Tool executor for onboarding flow
 * Handles execution of tools used in the AI onboarding process
 */

import { prisma } from '@workspace/database/client';
import Anthropic from '@anthropic-ai/sdk';
import { ONBOARDING_TOOL_NAMES } from './onboarding-tools';

export class OnboardingToolExecutor {

  /**
   * Execute a tool by name with given parameters
   */
  async execute(toolName: string, params: any, organizationId?: string): Promise<string> {
    console.log(`🔧 Executing onboarding tool: ${toolName}`, params);

    switch (toolName) {
      case ONBOARDING_TOOL_NAMES.WEB_SEARCH:
        return this.executeWebSearch(params);

      case ONBOARDING_TOOL_NAMES.GET_BOT_STYLES:
        return this.getBotStyles();

      case ONBOARDING_TOOL_NAMES.GET_BOT_STYLE_VARIABLES:
        return this.getBotStyleVariables(params.botStyleId);

      case ONBOARDING_TOOL_NAMES.SAVE_ONBOARDING_CONFIG:
        if (!organizationId) {
          throw new Error('Organization ID required for saving configuration');
        }
        return this.saveOnboardingConfig(params, organizationId);

      default:
        throw new Error(`Unknown tool: ${toolName}`);
    }
  }

  /**
   * Execute web search using Anthropic's web search API - NO FALLBACKS!
   */
  private async executeWebSearch(params: { query: string; focus?: string }): Promise<string> {
    console.log(`🔍 Real web search query: "${params.query}" (focus: ${params.focus || 'general'})`);

    // Use Anthropic's web search API directly - NO FALLBACKS!
    const webSearchResult = await this.performRealWebSearch(params.query, params.focus);
    console.log(`✅ Real web search completed for: ${params.query}`);
    return JSON.stringify(webSearchResult, null, 2);
  }

  /**
   * Get all available bot styles from database
   */
  private async getBotStyles(): Promise<string> {
    try {
      console.log('📋 Fetching all bot styles from database');

      const botStyles = await prisma.botStyle.findMany({
        select: {
          id: true,
          title: true,
          description: true,
          isDefault: true
        },
        orderBy: [
          { isDefault: 'desc' },
          { title: 'asc' }
        ]
      });

      console.log(`✅ Found ${botStyles.length} bot styles`);

      const result = {
        count: botStyles.length,
        botStyles: botStyles.map(style => ({
          id: style.id,
          title: style.title,
          description: style.description,
          isDefault: style.isDefault
        }))
      };

      return JSON.stringify(result, null, 2);

    } catch (error) {
      console.error(`❌ Failed to get bot styles:`, error);
      return `Error fetching bot styles: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }

  /**
   * Get variables for a specific bot style
   */
  private async getBotStyleVariables(botStyleId: string): Promise<string> {
    try {
      console.log(`📝 Fetching variables for bot style: ${botStyleId}`);

      const botStyle = await prisma.botStyle.findUnique({
        where: { id: botStyleId },
        include: {
          variables: {
            include: {
              group: true
            },
            orderBy: [
              { group: { order: 'asc' } },
              { order: 'asc' }
            ]
          }
        }
      });

      if (!botStyle) {
        return `Error: Bot style with ID ${botStyleId} not found`;
      }

      console.log(`✅ Found ${botStyle.variables.length} variables for bot style: ${botStyle.title}`);

      const result = {
        botStyleId: botStyle.id,
        botStyleTitle: botStyle.title,
        variableCount: botStyle.variables.length,
        variables: botStyle.variables.map(variable => ({
          id: variable.id,
          name: variable.name, // e.g., {{business_name}}
          displayName: variable.displayName,
          type: variable.type, // text, textarea, dropdown, array, structured_list
          required: variable.required,
          helpText: variable.helpText,
          defaultValue: variable.defaultValue,
          config: variable.config,
          group: variable.group ? {
            name: variable.group.name,
            description: variable.group.description
          } : null
        }))
      };

      return JSON.stringify(result, null, 2);

    } catch (error) {
      console.error(`❌ Failed to get bot style variables:`, error);
      return `Error fetching bot style variables: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }

  /**
   * Save the final onboarding configuration
   */
  private async saveOnboardingConfig(
    params: {
      botStyleId: string;
      variables: Record<string, any>;
      instagramHandle: string;
      businessAnalysis?: string;
    },
    organizationId: string
  ): Promise<string> {
    try {
      console.log(`💾 Saving onboarding config for organization: ${organizationId}`);

      // Update organization with selected bot style
      await prisma.organization.update({
        where: { id: organizationId },
        data: {
          botStyleId: params.botStyleId,
          // Store Instagram handle in a custom field if needed
          // You may need to add this to the Organization model
        }
      });

      // Save variable values
      const botStyleVariables = await prisma.botStyleVariable.findMany({
        where: { botStyleId: params.botStyleId }
      });

      // Delete existing variable values for this organization and bot style
      await prisma.organizationBotStyleVariable.deleteMany({
        where: {
          organizationId,
          botStyleId: params.botStyleId
        }
      });

      // Insert new variable values
      for (const variable of botStyleVariables) {
        const variableValue = params.variables[variable.name];

        if (variableValue !== undefined) {
          await prisma.organizationBotStyleVariable.create({
            data: {
              organizationId,
              botStyleId: params.botStyleId,
              variableId: variable.id,
              value: variableValue
            }
          });
        }
      }

      console.log(`✅ Onboarding configuration saved successfully`);

      const result = {
        success: true,
        message: 'Onboarding configuration saved successfully',
        botStyleId: params.botStyleId,
        savedVariables: Object.keys(params.variables).length,
        instagramHandle: params.instagramHandle
      };

      return JSON.stringify(result, null, 2);

    } catch (error) {
      console.error(`❌ Failed to save onboarding config:`, error);
      return `Error saving onboarding configuration: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }

  /**
   * Perform real web search using Anthropic's API
   */
  private async performRealWebSearch(query: string, focus?: string): Promise<any> {
    // Get Anthropic API key from platform settings
    const platformSettings = await prisma.platformSettings.findFirst();
    const anthropicApiKey = platformSettings?.anthropicApiKey;

    if (!anthropicApiKey) {
      throw new Error('Anthropic API key not configured in platform settings for web search');
    }

    const client = new Anthropic({
      apiKey: anthropicApiKey,
    });

    // Create a focused search query based on the focus area
    let searchQuery = query;
    if (focus === 'instagram') {
      searchQuery = `Instagram profile ${query} business information`;
    } else if (focus === 'business') {
      searchQuery = `${query} business company information`;
    }

    console.log(`🌐 Performing real web search: "${searchQuery}"`);

    try {
      const response = await client.messages.create({
        model: 'claude-sonnet-4-20250514',
        max_tokens: 1000,
        tools: [
          {
            type: 'web_search_20250305',
            name: 'web_search',
            max_uses: 3
          }
        ],
        messages: [
          {
            role: 'user',
            content: `Search for information about "${searchQuery}" and provide a comprehensive analysis including:

          1. Business type and industry
          2. Target audience
          3. Products or services offered
          4. Business goals and objectives
          5. Any relevant contact information
          6. Social media presence and engagement

          Format the response as structured JSON with the following format:
          {
            "query": "${query}",
            "focus": "${focus || 'general'}",
            "results": [
              {
                "type": "business_analysis",
                "data": {
                  "businessName": "...",
                  "industry": "...",
                  "businessType": "...",
                  "targetAudience": "...",
                  "products": [...],
                  "goals": [...],
                  "problems": [...],
                  "niche": "...",
                  "location": "...",
                  "website": "...",
                  "email": "..."
                }
              }
            ],
            "summary": "...",
            "recommendations": {
              "suggestedBotStyle": "...",
              "confidence": 0.8,
              "reasoning": "..."
            }
          }`
        }
      ]
    });

    console.log(`✅ Web search API call completed successfully`);
    console.log(`📊 Response type: ${typeof response}`);
    console.log(`📊 Response content length: ${response.content?.length || 0}`);

    // Extract the response content
    const content = response.content[0];
    console.log(`📊 Content type: ${content?.type}`);

    if (content.type === 'text') {
      console.log(`📊 Text content length: ${content.text?.length || 0}`);
      console.log(`📊 Text preview: ${content.text?.substring(0, 100)}...`);

      try {
        // Try to parse as JSON
        const parsed = JSON.parse(content.text);
        console.log(`✅ Successfully parsed JSON response`);
        return parsed;
      } catch (parseError) {
        console.log(`⚠️ Failed to parse as JSON, returning structured response`);
        // If not JSON, return structured response
        return {
          query,
          focus: focus || 'general',
          results: [
            {
              type: 'web_search_result',
              data: {
                content: content.text,
                source: 'anthropic_web_search'
              }
            }
          ],
          summary: content.text.substring(0, 200) + '...'
        };
      }
    }

    console.log(`❌ Unexpected content type: ${content?.type}`);
    throw new Error('Unexpected response format from Anthropic web search');

    } catch (error) {
      console.error(`❌ Web search API call failed:`, error);
      console.error(`❌ Error details:`, {
        message: error instanceof Error ? error.message : 'Unknown error',
        type: typeof error,
        stack: error instanceof Error ? error.stack : undefined
      });
      throw error;
    }
  }
}
