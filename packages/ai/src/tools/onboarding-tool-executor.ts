/**
 * Tool executor for onboarding flow
 * Handles execution of tools used in the AI onboarding process
 */

import { prisma } from '@workspace/database/client';
import { ONBOARDING_TOOL_NAMES } from './onboarding-tools';

export class OnboardingToolExecutor {

  /**
   * Execute a tool by name with given parameters
   */
  async execute(toolName: string, params: any, organizationId?: string): Promise<string> {
    console.log(`🔧 Executing onboarding tool: ${toolName}`, params);

    switch (toolName) {
      case ONBOARDING_TOOL_NAMES.WEB_SEARCH:
        return this.executeWebSearch(params);

      case ONBOARDING_TOOL_NAMES.GET_BOT_STYLES:
        return this.getBotStyles();

      case ONBOARDING_TOOL_NAMES.GET_BOT_STYLE_VARIABLES:
        return this.getBotStyleVariables(params.botStyleId);

      case ONBOARDING_TOOL_NAMES.SAVE_ONBOARDING_CONFIG:
        if (!organizationId) {
          throw new Error('Organization ID required for saving configuration');
        }
        return this.saveOnboardingConfig(params, organizationId);

      default:
        throw new Error(`Unknown tool: ${toolName}`);
    }
  }

  /**
   * Execute web search using available search capabilities
   */
  private async executeWebSearch(params: { query: string; focus?: string }): Promise<string> {
    try {
      console.log(`🔍 Web search query: "${params.query}" (focus: ${params.focus || 'general'})`);

      // NOTE: When using Anthropic's built-in web_search tool, this executor is not used.
      // This fallback returns mock data for non-Anthropic flows or local dev.
      const mockResults = this.getMockWebSearchResults(params.query, params.focus);
      console.log(`✅ Web search completed for: ${params.query}`);
      return JSON.stringify(mockResults, null, 2);

    } catch (error) {
      console.error(`❌ Web search failed:`, error);
      return `Error performing web search: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }

  /**
   * Get all available bot styles from database
   */
  private async getBotStyles(): Promise<string> {
    try {
      console.log('📋 Fetching all bot styles from database');

      const botStyles = await prisma.botStyle.findMany({
        select: {
          id: true,
          title: true,
          description: true,
          isDefault: true
        },
        orderBy: [
          { isDefault: 'desc' },
          { title: 'asc' }
        ]
      });

      console.log(`✅ Found ${botStyles.length} bot styles`);

      const result = {
        count: botStyles.length,
        botStyles: botStyles.map(style => ({
          id: style.id,
          title: style.title,
          description: style.description,
          isDefault: style.isDefault
        }))
      };

      return JSON.stringify(result, null, 2);

    } catch (error) {
      console.error(`❌ Failed to get bot styles:`, error);
      return `Error fetching bot styles: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }

  /**
   * Get variables for a specific bot style
   */
  private async getBotStyleVariables(botStyleId: string): Promise<string> {
    try {
      console.log(`📝 Fetching variables for bot style: ${botStyleId}`);

      const botStyle = await prisma.botStyle.findUnique({
        where: { id: botStyleId },
        include: {
          variables: {
            include: {
              group: true
            },
            orderBy: [
              { group: { order: 'asc' } },
              { order: 'asc' }
            ]
          }
        }
      });

      if (!botStyle) {
        return `Error: Bot style with ID ${botStyleId} not found`;
      }

      console.log(`✅ Found ${botStyle.variables.length} variables for bot style: ${botStyle.title}`);

      const result = {
        botStyleId: botStyle.id,
        botStyleTitle: botStyle.title,
        variableCount: botStyle.variables.length,
        variables: botStyle.variables.map(variable => ({
          id: variable.id,
          name: variable.name, // e.g., {{business_name}}
          displayName: variable.displayName,
          type: variable.type, // text, textarea, dropdown, array, structured_list
          required: variable.required,
          helpText: variable.helpText,
          defaultValue: variable.defaultValue,
          config: variable.config,
          group: variable.group ? {
            name: variable.group.name,
            description: variable.group.description
          } : null
        }))
      };

      return JSON.stringify(result, null, 2);

    } catch (error) {
      console.error(`❌ Failed to get bot style variables:`, error);
      return `Error fetching bot style variables: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }

  /**
   * Save the final onboarding configuration
   */
  private async saveOnboardingConfig(
    params: {
      botStyleId: string;
      variables: Record<string, any>;
      instagramHandle: string;
      businessAnalysis?: string;
    },
    organizationId: string
  ): Promise<string> {
    try {
      console.log(`💾 Saving onboarding config for organization: ${organizationId}`);

      // Update organization with selected bot style
      await prisma.organization.update({
        where: { id: organizationId },
        data: {
          botStyleId: params.botStyleId,
          // Store Instagram handle in a custom field if needed
          // You may need to add this to the Organization model
        }
      });

      // Save variable values
      const botStyleVariables = await prisma.botStyleVariable.findMany({
        where: { botStyleId: params.botStyleId }
      });

      // Delete existing variable values for this organization and bot style
      await prisma.organizationBotStyleVariable.deleteMany({
        where: {
          organizationId,
          botStyleId: params.botStyleId
        }
      });

      // Insert new variable values
      for (const variable of botStyleVariables) {
        const variableValue = params.variables[variable.name];

        if (variableValue !== undefined) {
          await prisma.organizationBotStyleVariable.create({
            data: {
              organizationId,
              botStyleId: params.botStyleId,
              variableId: variable.id,
              value: variableValue
            }
          });
        }
      }

      console.log(`✅ Onboarding configuration saved successfully`);

      const result = {
        success: true,
        message: 'Onboarding configuration saved successfully',
        botStyleId: params.botStyleId,
        savedVariables: Object.keys(params.variables).length,
        instagramHandle: params.instagramHandle
      };

      return JSON.stringify(result, null, 2);

    } catch (error) {
      console.error(`❌ Failed to save onboarding config:`, error);
      return `Error saving onboarding configuration: ${error instanceof Error ? error.message : 'Unknown error'}`;
    }
  }

  /**
   * Mock web search results for development
   */
  private getMockWebSearchResults(query: string, focus?: string) {
    const lowerQuery = query.toLowerCase();

    if (lowerQuery.includes('instagram') || focus === 'instagram') {
      return {
        query,
        focus: focus || 'instagram',
        results: [
          {
            type: 'instagram_profile',
            data: {
              username: query.replace('@', ''),
              displayName: 'Sample Business Account',
              bio: 'Premium coffee roasters ☕ | Organic & Fair Trade | Delivering worldwide 📦',
              followers: '15.2K',
              following: '842',
              posts: '1,247',
              verified: false,
              businessCategory: 'Food & Beverage',
              contactInfo: '<EMAIL>',
              website: 'www.samplecoffee.com'
            }
          },
          {
            type: 'business_info',
            data: {
              industry: 'Food & Beverage - Coffee',
              products: ['Premium Coffee Beans', 'Coffee Equipment', 'Merchandise'],
              targetAudience: 'Coffee enthusiasts, professionals, gift buyers',
              businessType: 'E-commerce with retail presence',
              established: '2019',
              location: 'Portland, Oregon'
            }
          }
        ],
        summary: 'This appears to be a premium coffee business focused on organic and fair trade products, with a strong e-commerce presence and growing social media following.'
      };
    }

    return {
      query,
      focus: focus || 'general',
      results: [
        {
          type: 'general_info',
          data: {
            relevance: 'moderate',
            summary: `Found general information related to "${query}". This appears to be a business in need of further analysis.`
          }
        }
      ],
      summary: `General search results for "${query}". More specific information may be needed.`
    };
  }
}
