/**
 * Tool definitions for AI onboarding flow
 */

export interface OnboardingTool {
  name: string;
  description: string;
  parameters: {
    type: 'object';
    properties: Record<string, any>;
    required: string[];
  };
}

/**
 * Web search tool for business analysis
 */
export const webSearchTool: OnboardingTool = {
  name: 'web_search',
  description: 'Search the web for information about a business, Instagram profile, or general topic. Use this to gather information about the user\'s business.',
  parameters: {
    type: 'object',
    properties: {
      query: {
        type: 'string',
        description: 'The search query to find information about the business'
      },
      focus: {
        type: 'string',
        enum: ['instagram', 'business', 'general'],
        description: 'Focus area for the search - instagram for profile info, business for company details, general for broader search'
      }
    },
    required: ['query']
  }
};

/**
 * Get all available bot styles
 */
export const getBotStylesTool: OnboardingTool = {
  name: 'get_bot_styles',
  description: 'Retrieve all available bot styles from the database with their descriptions to help select the best match for the user\'s business.',
  parameters: {
    type: 'object',
    properties: {},
    required: []
  }
};

/**
 * Get variables for a specific bot style
 */
export const getBotStyleVariablesTool: OnboardingTool = {
  name: 'get_bot_style_variables',
  description: 'Get all variables that need to be configured for a specific bot style, including their types, descriptions, and requirements.',
  parameters: {
    type: 'object',
    properties: {
      botStyleId: {
        type: 'string',
        description: 'The UUID of the bot style to get variables for'
      }
    },
    required: ['botStyleId']
  }
};

/**
 * Save the onboarding configuration
 */
export const saveOnboardingConfigTool: OnboardingTool = {
  name: 'save_onboarding_config',
  description: 'Save the final onboarding configuration including selected bot style and filled variables.',
  parameters: {
    type: 'object',
    properties: {
      botStyleId: {
        type: 'string',
        description: 'The selected bot style UUID'
      },
      variables: {
        type: 'object',
        description: 'Object with variable names as keys and their filled values'
      },
      instagramHandle: {
        type: 'string',
        description: 'The user\'s Instagram handle'
      },
      businessAnalysis: {
        type: 'string',
        description: 'Summary of the business analysis'
      }
    },
    required: ['botStyleId', 'variables', 'instagramHandle']
  }
};

/**
 * All onboarding tools combined
 */
export const onboardingTools: OnboardingTool[] = [
  webSearchTool,
  getBotStylesTool,
  getBotStyleVariablesTool,
  saveOnboardingConfigTool
];

/**
 * Tool names for easy reference
 */
export const ONBOARDING_TOOL_NAMES = {
  WEB_SEARCH: 'web_search',
  GET_BOT_STYLES: 'get_bot_styles',
  GET_BOT_STYLE_VARIABLES: 'get_bot_style_variables',
  SAVE_ONBOARDING_CONFIG: 'save_onboarding_config'
} as const;