{"name": "@workspace/aidollars", "version": "0.0.0", "private": true, "exports": {".": {"types": "./dist/index.d.ts", "default": "./dist/index.js"}}, "main": "./dist/index.js", "types": "./dist/index.d.ts", "scripts": {"build": "tsup", "dev": "tsup --watch"}, "dependencies": {"@prisma/client": "6.9.0", "@workspace/database": "workspace:*"}, "devDependencies": {"@types/node": "22.15.30", "@workspace/typescript-config": "workspace:*", "tsup": "^8.3.5", "typescript": "5.8.3"}}