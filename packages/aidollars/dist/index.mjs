// src/balance.ts
import { prisma } from "@workspace/database/client";
var InsufficientBalanceError = class extends Error {
  constructor(balance, required = parseFloat(process.env.AI_REQUEST_COST || "0.10")) {
    super(`Insufficient AI Dollars balance: $${balance.toFixed(2)} (need $${required.toFixed(2)})`);
    this.balance = balance;
    this.required = required;
    this.name = "InsufficientBalanceError";
  }
};
var BalanceService = class _BalanceService {
  static MONTHLY_CHARGE_AMOUNT = 1e3;
  static INITIAL_CREDIT_AMOUNT = 500;
  static getAiRequestCost() {
    return parseFloat(process.env.AI_REQUEST_COST || "0.10");
  }
  /**
   * Get organization's AI Dollars balance
   */
  async getBalance(organizationId) {
    const balance = await prisma.aIDollarsBalance.findUnique({
      where: { organizationId }
    });
    if (!balance) {
      return null;
    }
    return {
      balance: Number(balance.balance),
      registeredAt: balance.registeredAt,
      lastMonthlyChargeAt: balance.lastMonthlyChargeAt || void 0,
      lastTopupAt: balance.lastTopupAt || void 0,
      nextMonthlyChargeAt: balance.nextMonthlyChargeAt || void 0
    };
  }
  /**
   * Initialize balance for new organization (registration)
   */
  async initializeBalance(organizationId) {
    const now = /* @__PURE__ */ new Date();
    const firstChargeDate = new Date(now);
    firstChargeDate.setDate(firstChargeDate.getDate() + 14);
    const nextMonthlyDate = this.calculateNextMonthlyChargeDate(now);
    const balance = await prisma.aIDollarsBalance.create({
      data: {
        organizationId,
        balance: _BalanceService.INITIAL_CREDIT_AMOUNT,
        // $500 initial credit
        registeredAt: now,
        nextMonthlyChargeAt: firstChargeDate
        // First charge after 14 days
      }
    });
    return {
      balance: Number(balance.balance),
      registeredAt: balance.registeredAt,
      lastMonthlyChargeAt: void 0,
      lastTopupAt: void 0,
      nextMonthlyChargeAt: balance.nextMonthlyChargeAt || void 0
    };
  }
  /**
   * Smart charging logic - handles monthly vs auto top-up priority
   */
  async checkAndCharge(organizationId) {
    const balance = await this.getBalance(organizationId);
    if (!balance) {
      throw new Error(`No AI Dollars balance found for organization ${organizationId}`);
    }
    const now = /* @__PURE__ */ new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    if (balance.nextMonthlyChargeAt) {
      const chargeDate = new Date(
        balance.nextMonthlyChargeAt.getFullYear(),
        balance.nextMonthlyChargeAt.getMonth(),
        balance.nextMonthlyChargeAt.getDate()
      );
      if (today.getTime() >= chargeDate.getTime()) {
        await this.chargeMonthly(organizationId);
        return {
          charged: true,
          amount: _BalanceService.MONTHLY_CHARGE_AMOUNT,
          reason: "monthly_subscription"
        };
      }
    }
    if (balance.balance < _BalanceService.getAiRequestCost()) {
      await this.chargeTopup(organizationId);
      return {
        charged: true,
        amount: _BalanceService.MONTHLY_CHARGE_AMOUNT,
        reason: "auto_topup"
      };
    }
    return { charged: false, amount: 0, reason: "sufficient_balance" };
  }
  /**
   * Charge monthly subscription
   */
  async chargeMonthly(organizationId) {
    const now = /* @__PURE__ */ new Date();
    const nextChargeDate = this.calculateNextMonthlyChargeDate(now);
    await prisma.aIDollarsBalance.update({
      where: { organizationId },
      data: {
        balance: { increment: _BalanceService.MONTHLY_CHARGE_AMOUNT },
        lastMonthlyChargeAt: now,
        nextMonthlyChargeAt: nextChargeDate,
        updatedAt: now
      }
    });
    console.log(`\u{1F4B3} Monthly charge: +$${_BalanceService.MONTHLY_CHARGE_AMOUNT} for org ${organizationId}`);
  }
  /**
   * Charge auto top-up
   */
  async chargeTopup(organizationId) {
    const now = /* @__PURE__ */ new Date();
    await prisma.aIDollarsBalance.update({
      where: { organizationId },
      data: {
        balance: { increment: _BalanceService.MONTHLY_CHARGE_AMOUNT },
        lastTopupAt: now,
        updatedAt: now
      }
    });
    console.log(`\u{1F504} Auto top-up: +$${_BalanceService.MONTHLY_CHARGE_AMOUNT} for org ${organizationId}`);
  }
  /**
   * Deduct balance for AI request
   */
  async deductBalance(organizationId, description) {
    return await prisma.$transaction(async (tx) => {
      const currentBalance = await tx.aIDollarsBalance.findUnique({
        where: { organizationId }
      });
      if (!currentBalance) {
        throw new Error(`No AI Dollars balance found for organization ${organizationId}`);
      }
      const currentBalanceNum = Number(currentBalance.balance);
      const aiRequestCost = _BalanceService.getAiRequestCost();
      const newBalance = currentBalanceNum - aiRequestCost;
      await tx.aIDollarsBalance.update({
        where: { organizationId },
        data: { balance: newBalance }
      });
      console.log(`\u{1F4B0} Deducted $${aiRequestCost.toFixed(2)} for ${description}. New balance: $${newBalance.toFixed(2)}`);
      return { success: true, newBalance };
    });
  }
  /**
   * Calculate next monthly charge date, handling edge cases
   */
  calculateNextMonthlyChargeDate(fromDate) {
    const next = new Date(fromDate);
    const originalDay = fromDate.getDate();
    next.setMonth(next.getMonth() + 1);
    const lastDayOfMonth = new Date(next.getFullYear(), next.getMonth() + 1, 0).getDate();
    if (originalDay > lastDayOfMonth) {
      next.setDate(lastDayOfMonth);
    } else {
      next.setDate(originalDay);
    }
    return next;
  }
  /**
   * Get usage statistics from existing AiUsage table
   */
  async getUsageStats(organizationId, days = 30) {
    const since = /* @__PURE__ */ new Date();
    since.setDate(since.getDate() - days);
    const usage = await prisma.aiUsage.aggregate({
      where: {
        organizationId,
        createdAt: { gte: since }
      },
      _count: { id: true },
      _sum: { totalCost: true }
    });
    const requestCount = usage._count.id || 0;
    const totalCost = Number(usage._sum.totalCost || 0);
    const aiDollarsCost = requestCount * _BalanceService.getAiRequestCost();
    return {
      requestCount,
      totalProviderCost: totalCost,
      aiDollarsCost,
      dailyAverage: requestCount / days
    };
  }
};
export {
  BalanceService,
  InsufficientBalanceError
};
