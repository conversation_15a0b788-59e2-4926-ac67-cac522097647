interface BalanceInfo {
    balance: number;
    registeredAt: Date;
    lastMonthlyChargeAt?: Date;
    lastTopupAt?: Date;
    nextMonthlyChargeAt?: Date;
}
declare class InsufficientBalanceError extends Error {
    balance: number;
    required: number;
    constructor(balance: number, required?: number);
}
declare class BalanceService {
    private static readonly MONTHLY_CHARGE_AMOUNT;
    private static readonly INITIAL_CREDIT_AMOUNT;
    private static getAiRequestCost;
    /**
     * Get organization's AI Dollars balance
     */
    getBalance(organizationId: string): Promise<BalanceInfo | null>;
    /**
     * Initialize balance for new organization (registration)
     */
    initializeBalance(organizationId: string): Promise<BalanceInfo>;
    /**
     * Smart charging logic - handles monthly vs auto top-up priority
     */
    checkAndCharge(organizationId: string): Promise<{
        charged: boolean;
        amount: number;
        reason: string;
    }>;
    /**
     * Charge monthly subscription
     */
    private chargeMonthly;
    /**
     * Charge auto top-up
     */
    private chargeTopup;
    /**
     * Deduct balance for AI request
     */
    deductBalance(organizationId: string, description: string): Promise<{
        success: boolean;
        newBalance: number;
    }>;
    /**
     * Calculate next monthly charge date, handling edge cases
     */
    private calculateNextMonthlyChargeDate;
    /**
     * Get usage statistics from existing AiUsage table
     */
    getUsageStats(organizationId: string, days?: number): Promise<{
        requestCount: number;
        totalProviderCost: number;
        aiDollarsCost: number;
        dailyAverage: number;
    }>;
}

export { type BalanceInfo, BalanceService, InsufficientBalanceError };
