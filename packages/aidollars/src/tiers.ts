import { prisma } from '@workspace/database/client';

export interface TierInfo {
  tierLevel: number;
  topupAmount: number;
  usageThreshold: number;
  isActive: boolean;
}

export interface SubscriptionTier {
  name: string;
  pricePerRequest: number;
  tierLevel: number;
}

export class TiersService {
  // Default tier configuration
  private static readonly DEFAULT_TIERS: TierInfo[] = [
    { tierLevel: 1, topupAmount: 10, usageThreshold: 10, isActive: true }, // $10 for <10 requests/day
    { tierLevel: 2, topupAmount: 50, usageThreshold: 50, isActive: true }, // $50 for 10-50 requests/day
    { tierLevel: 3, topupAmount: 100, usageThreshold: 200, isActive: true }, // $100 for 50-200 requests/day
    { tierLevel: 4, topupAmount: 1000, usageThreshold: 1000, isActive: true }, // $1000 for 200+ requests/day
  ];

  // Subscription tiers (for display purposes)
  private static readonly SUBSCRIPTION_TIERS: SubscriptionTier[] = [
    { name: 'Starter', pricePerRequest: 0.10, tierLevel: 1 },
    { name: 'Professional', pricePerRequest: 0.10, tierLevel: 2 },
    { name: 'Business', pricePerRequest: 0.10, tierLevel: 3 },
    { name: 'Enterprise', pricePerRequest: 0.10, tierLevel: 4 },
  ];

  /**
   * Initialize default tiers for organization
   */
  async initializeTiers(organizationId: string): Promise<TierInfo[]> {
    const existingTiers = await prisma.aIDollarsTopupRule.findMany({
      where: { organizationId },
    });

    if (existingTiers.length > 0) {
      return existingTiers.map((tier: any) => ({
        tierLevel: tier.tierLevel,
        topupAmount: Number(tier.topupAmount),
        usageThreshold: Number(tier.usageThreshold),
        isActive: tier.isActive,
      }));
    }

    // Create default tiers
    const createdTiers = await Promise.all(
      TiersService.DEFAULT_TIERS.map((tier: TierInfo) =>
        prisma.aIDollarsTopupRule.create({
          data: {
            organizationId,
            tierLevel: tier.tierLevel,
            topupAmount: tier.topupAmount,
            usageThreshold: tier.usageThreshold,
            isActive: tier.isActive,
          },
        })
      )
    );

    return createdTiers.map((tier: any) => ({
      tierLevel: tier.tierLevel,
      topupAmount: Number(tier.topupAmount),
      usageThreshold: Number(tier.usageThreshold),
      isActive: tier.isActive,
    }));
  }

  /**
   * Get appropriate tier based on usage rate
   */
  async getAppropriateTopupAmount(
    organizationId: string,
    dailyUsageRate: number
  ): Promise<number> {
    const tiers = await prisma.aIDollarsTopupRule.findMany({
      where: { organizationId, isActive: true },
      orderBy: { tierLevel: 'asc' },
    });

    if (tiers.length === 0) {
      // Initialize tiers if none exist
      await this.initializeTiers(organizationId);
      return 10; // Default to smallest tier
    }

    // Find appropriate tier based on usage rate
    for (const tier of tiers) {
      if (dailyUsageRate <= Number(tier.usageThreshold)) {
        return Number(tier.topupAmount);
      }
    }

    // If usage exceeds all thresholds, use the highest tier
    return Number(tiers[tiers.length - 1].topupAmount);
  }

  /**
   * Calculate daily usage rate for organization
   */
  async calculateDailyUsageRate(organizationId: string, days = 7): Promise<number> {
    const since = new Date();
    since.setDate(since.getDate() - days);

    const transactions = await prisma.aIDollarsTransaction.findMany({
      where: {
        organizationId,
        type: 'DEBIT',
        createdAt: { gte: since },
      },
    });

    const totalRequests = transactions.length;
    return totalRequests / days;
  }

  /**
   * Get subscription tier name based on usage/tier level
   */
  getSubscriptionTierName(dailyUsageRate: number): string {
    if (dailyUsageRate <= 10) return 'Starter';
    if (dailyUsageRate <= 50) return 'Professional';
    if (dailyUsageRate <= 200) return 'Business';
    return 'Enterprise';
  }

  /**
   * Get all subscription tier options
   */
  getAllSubscriptionTiers(): SubscriptionTier[] {
    return [...TiersService.SUBSCRIPTION_TIERS];
  }

  /**
   * Get organization's current tier info
   */
  async getCurrentTierInfo(organizationId: string): Promise<{
    currentTier: SubscriptionTier;
    dailyUsageRate: number;
    nextTopupAmount: number;
  }> {
    const dailyUsageRate = await this.calculateDailyUsageRate(organizationId);
    const tierName = this.getSubscriptionTierName(dailyUsageRate);
    const nextTopupAmount = await this.getAppropriateTopupAmount(organizationId, dailyUsageRate);

    const currentTier = TiersService.SUBSCRIPTION_TIERS.find(tier => tier.name === tierName) ||
                       TiersService.SUBSCRIPTION_TIERS[0];

    return {
      currentTier,
      dailyUsageRate,
      nextTopupAmount,
    };
  }
}