import { prisma } from '@workspace/database/client';

export interface BalanceInfo {
  balance: number;
  registeredAt: Date;
  lastMonthlyChargeAt?: Date;
  lastTopupAt?: Date;
  nextMonthlyChargeAt?: Date;
}

export class InsufficientBalanceError extends Error {
  constructor(
    public balance: number,
    public required: number = parseFloat(process.env.AI_REQUEST_COST || '0.10')
  ) {
    super(`Insufficient AI Dollars balance: $${balance.toFixed(2)} (need $${required.toFixed(2)})`);
    this.name = 'InsufficientBalanceError';
  }
}

export class BalanceService {
  private static readonly MONTHLY_CHARGE_AMOUNT = 1000.00;
  private static readonly INITIAL_CREDIT_AMOUNT = 500.00;
  private static getAiRequestCost(): number {
    return parseFloat(process.env.AI_REQUEST_COST || '0.10');
  }

  /**
   * Get organization's AI Dollars balance
   */
  async getBalance(organizationId: string): Promise<BalanceInfo | null> {
    const balance = await prisma.aIDollarsBalance.findUnique({
      where: { organizationId },
    });

    if (!balance) {
      return null;
    }

    return {
      balance: Number(balance.balance),
      registeredAt: balance.registeredAt,
      lastMonthlyChargeAt: balance.lastMonthlyChargeAt || undefined,
      lastTopupAt: balance.lastTopupAt || undefined,
      nextMonthlyChargeAt: balance.nextMonthlyChargeAt || undefined,
    };
  }

  /**
   * Initialize balance for new organization (registration)
   */
  async initializeBalance(organizationId: string): Promise<BalanceInfo> {
    const now = new Date();
    const firstChargeDate = new Date(now);
    firstChargeDate.setDate(firstChargeDate.getDate() + 14); // 14 days later

    const nextMonthlyDate = this.calculateNextMonthlyChargeDate(now);

    const balance = await prisma.aIDollarsBalance.create({
      data: {
        organizationId,
        balance: BalanceService.INITIAL_CREDIT_AMOUNT, // $500 initial credit
        registeredAt: now,
        nextMonthlyChargeAt: firstChargeDate, // First charge after 14 days
      },
    });

    return {
      balance: Number(balance.balance),
      registeredAt: balance.registeredAt,
      lastMonthlyChargeAt: undefined,
      lastTopupAt: undefined,
      nextMonthlyChargeAt: balance.nextMonthlyChargeAt || undefined,
    };
  }

  /**
   * Smart charging logic - handles monthly vs auto top-up priority
   */
  async checkAndCharge(organizationId: string): Promise<{ charged: boolean; amount: number; reason: string }> {
    const balance = await this.getBalance(organizationId);

    if (!balance) {
      throw new Error(`No AI Dollars balance found for organization ${organizationId}`);
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    // Check if today is monthly charge day
    if (balance.nextMonthlyChargeAt) {
      const chargeDate = new Date(
        balance.nextMonthlyChargeAt.getFullYear(),
        balance.nextMonthlyChargeAt.getMonth(),
        balance.nextMonthlyChargeAt.getDate()
      );

      if (today.getTime() >= chargeDate.getTime()) {
        // Monthly charge has priority
        await this.chargeMonthly(organizationId);
        return {
          charged: true,
          amount: BalanceService.MONTHLY_CHARGE_AMOUNT,
          reason: 'monthly_subscription'
        };
      }
    }

    // Only auto top-up if not monthly day and balance insufficient
    if (balance.balance < BalanceService.getAiRequestCost()) {
      await this.chargeTopup(organizationId);
      return {
        charged: true,
        amount: BalanceService.MONTHLY_CHARGE_AMOUNT,
        reason: 'auto_topup'
      };
    }

    return { charged: false, amount: 0, reason: 'sufficient_balance' };
  }

  /**
   * Charge monthly subscription
   */
  private async chargeMonthly(organizationId: string): Promise<void> {
    const now = new Date();
    const nextChargeDate = this.calculateNextMonthlyChargeDate(now);

    await prisma.aIDollarsBalance.update({
      where: { organizationId },
      data: {
        balance: { increment: BalanceService.MONTHLY_CHARGE_AMOUNT },
        lastMonthlyChargeAt: now,
        nextMonthlyChargeAt: nextChargeDate,
        updatedAt: now,
      },
    });

    console.log(`💳 Monthly charge: +$${BalanceService.MONTHLY_CHARGE_AMOUNT} for org ${organizationId}`);
  }

  /**
   * Charge auto top-up
   */
  private async chargeTopup(organizationId: string): Promise<void> {
    const now = new Date();

    await prisma.aIDollarsBalance.update({
      where: { organizationId },
      data: {
        balance: { increment: BalanceService.MONTHLY_CHARGE_AMOUNT },
        lastTopupAt: now,
        updatedAt: now,
      },
    });

    console.log(`🔄 Auto top-up: +$${BalanceService.MONTHLY_CHARGE_AMOUNT} for org ${organizationId}`);
  }

  /**
   * Deduct balance for AI request
   */
  async deductBalance(organizationId: string, description: string): Promise<{ success: boolean; newBalance: number }> {
    return await prisma.$transaction(async (tx: any) => {
      const currentBalance = await tx.aIDollarsBalance.findUnique({
        where: { organizationId },
      });

      if (!currentBalance) {
        throw new Error(`No AI Dollars balance found for organization ${organizationId}`);
      }

      const currentBalanceNum = Number(currentBalance.balance);
      const aiRequestCost = BalanceService.getAiRequestCost();
      const newBalance = currentBalanceNum - aiRequestCost;

      await tx.aIDollarsBalance.update({
        where: { organizationId },
        data: { balance: newBalance },
      });

      console.log(`💰 Deducted $${aiRequestCost.toFixed(2)} for ${description}. New balance: $${newBalance.toFixed(2)}`);

      return { success: true, newBalance };
    });
  }

  /**
   * Calculate next monthly charge date, handling edge cases
   */
  private calculateNextMonthlyChargeDate(fromDate: Date): Date {
    const next = new Date(fromDate);
    const originalDay = fromDate.getDate();

    // Move to next month
    next.setMonth(next.getMonth() + 1);

    // Handle edge cases like Jan 31 -> Feb 28/29
    const lastDayOfMonth = new Date(next.getFullYear(), next.getMonth() + 1, 0).getDate();

    if (originalDay > lastDayOfMonth) {
      next.setDate(lastDayOfMonth);
    } else {
      next.setDate(originalDay);
    }

    return next;
  }

  /**
   * Get usage statistics from existing AiUsage table
   */
  async getUsageStats(organizationId: string, days = 30) {
    const since = new Date();
    since.setDate(since.getDate() - days);

    const usage = await prisma.aiUsage.aggregate({
      where: {
        organizationId,
        createdAt: { gte: since },
      },
      _count: { id: true },
      _sum: { totalCost: true },
    });

    const requestCount = usage._count.id || 0;
    const totalCost = Number(usage._sum.totalCost || 0);
    const aiDollarsCost = requestCount * BalanceService.getAiRequestCost();

    return {
      requestCount,
      totalProviderCost: totalCost,
      aiDollarsCost,
      dailyAverage: requestCount / days,
    };
  }
}