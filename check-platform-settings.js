// Quick check of platform settings
const { PrismaClient } = require('@prisma/client');

const prisma = new PrismaClient({
  datasourceUrl: process.env.DATABASE_URL
});

async function checkPlatformSettings() {
  try {
    console.log('🔍 Checking platform settings...');
    const settings = await prisma.platformSettings.findFirst();
    
    if (!settings) {
      console.log('❌ No platform settings found!');
      return;
    }
    
    console.log('📊 Platform Settings:');
    console.log(`  - AI Model: ${settings.aiModel}`);
    console.log(`  - Anthropic API Key: ${settings.anthropicApiKey ? '✅ Set' : '❌ Not set'}`);
    console.log(`  - OpenRouter API Key: ${settings.openRouterApiKey ? '✅ Set' : '❌ Not set'}`);
    console.log(`  - ZAI API Key: ${settings.zaiApiKey ? '✅ Set' : '❌ Not set'}`);
    console.log(`  - Enable Thinking: ${settings.enableThinking}`);
    console.log(`  - Temperature: ${settings.temperature}`);
    console.log(`  - Top P: ${settings.topP}`);
    
    // Check if current model is Anthropic
    const isAnthropic = settings.aiModel.includes('claude') || settings.aiModel.includes('anthropic');
    console.log(`  - Is Anthropic Model: ${isAnthropic}`);
    
    if (isAnthropic && !settings.anthropicApiKey) {
      console.log('⚠️ WARNING: Anthropic model selected but no API key configured!');
    }
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkPlatformSettings();
