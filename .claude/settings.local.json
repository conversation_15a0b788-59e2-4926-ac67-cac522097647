{"permissions": {"allow": ["WebFetch(domain:developers.facebook.com)", "WebSearch", "Bash(pnpm typecheck:*)", "Bash(pnpm --filter database typecheck)", "Bash(pnpm --filter dashboard typecheck)", "Bash(pnpm --filter @workspace/routes build)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "Bash(npx shadcn@latest add:*)", "WebFetch(domain:shadcn-chatbot-kit.vercel.app)", "WebFetch(domain:www.shadcn.io)", "Bash(pnpm --filter database generate)", "WebFetch(domain:openrouter.ai)", "WebFetch(domain:docs.anthropic.com)", "Bash(grep:*)", "Bash(pnpm --filter @workspace/ai build)", "Bash(rm:*)", "Bash(pnpm dev:*)", "Bash(pnpm --filter database migrate dev --name \"add_ai_debug_data_tables\")", "Bash(pnpm --filter marketing typecheck)", "Bash(pnpm --filter public-api typecheck)", "Bash(pnpm --filter @workspace/ai typecheck)", "Bash(pnpm --filter @workspace/database typecheck)", "Bash(pnpm --filter database push)", "Bash(pnpm --filter database migrate dev --name \"remove_followups_style\")", "Bash(pnpm --filter database migrate dev --name \"initial_production_schema\")", "Bash(pnpm --filter database migrate status)", "Bash(npm install)", "Bash(npm run build:*)", "Read(/Users/<USER>/Documents/GitHub/**)", "Read(/Users/<USER>/Documents/GitHub/AIChromaPRO-chrome-extension/**)", "Read(/Users/<USER>/Documents/GitHub/AIChromaPRO-chrome-extension/src/**)", "Read(/Users/<USER>/Documents/GitHub/AIChromaPRO-chrome-extension/src/content/**)", "Read(/Users/<USER>/Documents/GitHub/AIChromaPRO-chrome-extension/src/content/**)", "Read(/Users/<USER>/Documents/GitHub/AIChromaPRO-chrome-extension/src/content/**)", "Read(/Users/<USER>/Documents/GitHub/AIChromaPRO-chrome-extension/src/background/**)", "Read(/Users/<USER>/Documents/GitHub/AIChromaPRO-chrome-extension/src/injected/**)", "Read(//Users/<USER>/Documents/GitHub/AIChromaPRO-chrome-extension/src/background/**)", "Read(//Users/<USER>/Documents/GitHub/AIChromaPRO-chrome-extension/**)", "Bash(git restore:*)", "<PERSON><PERSON>(git clean:*)", "Bash(node:*)", "Bash(ls:*)", "Bash(pnpm --filter database migrate dev --name \"add_bot_style_variables\")", "Bash(npx prisma db push:*)", "Bash(npx ts-node:*)", "Bash(npx tsx:*)", "WebFetch(domain:developer.calendly.com)", "Bash(pnpm --filter dashboard build)", "Bash(pnpm --filter database migrate resolve --applied \"20250830125906_production_ready\")", "Bash(pnpm --filter database migrate resolve --applied \"20250823095410_add_instagram_followups_enabled\")", "Bash(pnpm --filter database migrate resolve --applied \"20250823141912_increase_max_tokens_default\")", "Bash(pnpm --filter database migrate resolve --applied \"20250824215013_add_ai_usage_tracking\")", "Bash(pnpm --filter database migrate resolve --applied \"20250822130844_initial_instagram_bot_schema\")", "Bash(PGPASSWORD:*)", "Bash(pnpm add:*)", "Bash(pnpm --filter @workspace/aidollars typecheck)", "Bash(pnpm --filter @workspace/billing typecheck)", "Bash(pnpm install:*)"], "deny": [], "ask": []}}