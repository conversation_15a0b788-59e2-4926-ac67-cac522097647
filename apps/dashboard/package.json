{"name": "dashboard", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "next dev --port 3000 --turbopack", "build": "next build --turbopack", "start": "next start --port 3000", "analyze": "BUNDLE_ANALYZE=both next build --turbopack", "clean": "git clean -xdf .cache .next .turbo build dist node_modules", "format": "prettier --check \"**/*.{js,cjs,mjs,ts,tsx,mdx}\" --ignore-path=\"../../.prettierignore\"", "format:fix": "prettier --write \"**/*.{js,cjs,mjs,ts,tsx,mdx}\" --ignore-path=\"../../.prettierignore\"", "lint": "eslint . --max-warnings 0", "lint:fix": "eslint --fix .", "typecheck": "tsc --noEmit"}, "dependencies": {"@ai-sdk/anthropic": "^2.0.17", "@ai-sdk/openai": "^2.0.30", "@ai-sdk/react": "^2.0.44", "@dnd-kit/core": "6.3.1", "@dnd-kit/modifiers": "9.0.0", "@dnd-kit/sortable": "10.0.0", "@dnd-kit/utilities": "3.2.2", "@ebay/nice-modal-react": "1.2.13", "@hookform/resolvers": "5.1.0", "@t3-oss/env-nextjs": "0.13.6", "@tanstack/react-table": "8.21.3", "@types/handlebars": "^4.1.0", "@workspace/ai": "workspace:*", "@workspace/aidollars": "workspace:*", "@workspace/analytics": "workspace:*", "@workspace/api-keys": "workspace:*", "@workspace/auth": "workspace:*", "@workspace/billing": "workspace:*", "@workspace/common": "workspace:*", "@workspace/database": "workspace:*", "@workspace/email": "workspace:*", "@workspace/monitoring": "workspace:*", "@workspace/routes": "workspace:*", "@workspace/ui": "workspace:*", "ai": "^5.0.44", "date-fns": "4.1.0", "exceljs": "4.4.0", "file-saver": "2.0.5", "framer-motion": "^11.18.2", "handlebars": "^4.7.8", "lucide-react": "0.513.0", "markdown-it": "14.1.0", "nanoid": "^5.1.5", "next": "15.3.3", "next-safe-action": "8.0.2", "next-secure-headers": "2.2.0", "nuqs": "2.4.3", "otplib": "12.0.1", "p-limit": "^7.1.1", "qrcode": "1.5.4", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "7.57.0", "react-is": "19.0.0", "recharts": "2.15.3", "sanitize-html": "2.17.0", "server-only": "0.0.1", "sharp": "0.34.2", "swr": "2.3.0", "turndown": "7.2.0", "uuid": "11.1.0", "vaul": "1.1.2", "zod": "3.25.56"}, "devDependencies": {"@aws-sdk/client-s3": "3.826.0", "@next/bundle-analyzer": "15.3.3", "@types/file-saver": "2.0.7", "@types/markdown-it": "14.1.2", "@types/node": "22.15.30", "@types/qrcode": "1.5.5", "@types/react": "19.0.8", "@types/react-dom": "19.0.3", "@types/sanitize-html": "2.16.0", "@types/turndown": "5.0.5", "@types/uuid": "10.0.0", "@workspace/eslint-config": "workspace:*", "@workspace/prettier-config": "workspace:*", "@workspace/typescript-config": "workspace:*"}, "prettier": "@workspace/prettier-config"}