{"permissions": {"allow": ["Bash(pnpm typecheck:*)", "Read(//Users/<USER>/Documents/GitHub/AIChromaPRO/packages/ai/src/**)", "Read(//Users/<USER>/Documents/GitHub/AIChromaPRO/**)", "Bash(pnpm --filter aidollars build)", "Bash(pnpm --filter database generate)", "Bash(pnpm install:*)", "Bash(pnpm --filter dashboard typecheck)", "Bash(pnpm --filter dashboard dev)", "WebFetch(domain:github.com)", "WebFetch(domain:demo.chat-sdk.dev)", "Bash(pnpm:*)", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "WebSearch", "WebFetch(domain:ai-sdk.dev)"], "deny": [], "ask": []}}