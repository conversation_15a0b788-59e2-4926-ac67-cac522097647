import { streamText, tool, stepCountIs } from 'ai';
import { createAnthropic } from '@ai-sdk/anthropic';
import { createOpenAI } from '@ai-sdk/openai';
import { NextRequest } from 'next/server';
import { z } from 'zod';

import { getAuthContext } from '@workspace/auth/context';
import { prisma } from '@workspace/database/client';
import { OnboardingToolExecutor } from '@workspace/ai';

export const maxDuration = 30;

export async function POST(req: NextRequest) {
  try {
    // Get auth context
    const authContext = await getAuthContext();
    if (!authContext.session) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Parse request
    const body = await req.json();
    const { messages } = body;

    console.log('📝 Request body:', body);
    console.log('📝 Messages:', messages);

    if (!messages || !Array.isArray(messages)) {
      return new Response('Invalid messages format', { status: 400 });
    }

    // Get platform settings for AI configuration
    const platformSettings = await prisma.platformSettings.findFirst();
    if (!platformSettings) {
      return new Response('Platform settings not configured', { status: 500 });
    }

    // Determine which AI provider to use
    let model: any;
    const selectedModel = platformSettings.aiModel || 'openai/gpt-4o-mini';
    const isAnthropic = selectedModel.includes('claude') || selectedModel.includes('anthropic');

    if (isAnthropic) {
      if (!platformSettings.anthropicApiKey) {
        return new Response('Anthropic API key not configured', { status: 500 });
      }
      // Use Claude model - ensure proper model name format
      const anthropicClient = createAnthropic({
        apiKey: platformSettings.anthropicApiKey,
      });
      // Remove any prefix and use the model name directly
      const modelName = selectedModel.replace('anthropic/', '').replace('claude/', '');
      model = anthropicClient(modelName);
      console.log(`🤖 Using Anthropic model: ${modelName}`);
    } else {
      if (!platformSettings.openRouterApiKey) {
        return new Response('OpenRouter API key not configured', { status: 500 });
      }
      // Use OpenAI/OpenRouter model
      const openaiClient = createOpenAI({
        apiKey: platformSettings.openRouterApiKey,
        baseURL: 'https://openrouter.ai/api/v1',
      });
      model = openaiClient(selectedModel.replace('openai/', ''));
    }

    // Initialize tool executor
    const toolExecutor = new OnboardingToolExecutor();

    // Define tools for AI SDK (custom tools only). For Anthropic, use built-in web_search tool.
    const tools: Record<string, any> = {
      get_bot_styles: tool({
        description: 'Retrieve all available bot styles from the database',
        inputSchema: z.object({}),
        execute: async () => {
          console.log('🔧 Executing get_bot_styles tool');
          const result = await toolExecutor.execute('get_bot_styles', {});
          console.log('✅ Bot styles tool completed');
          return result;
        }
      }),
      get_bot_style_variables: tool({
        description: 'Get all variables for a specific bot style',
        inputSchema: z.object({
          botStyleId: z.string().describe('The UUID of the bot style')
        }),
        execute: async ({ botStyleId }) => {
          console.log('🔧 Executing get_bot_style_variables tool with params:', { botStyleId });
          const result = await toolExecutor.execute('get_bot_style_variables', { botStyleId });
          console.log('✅ Bot style variables tool completed');
          return result;
        }
      }),
      save_onboarding_config: tool({
        description: 'Save the final onboarding configuration',
        inputSchema: z.object({
          botStyleId: z.string().describe('The selected bot style UUID'),
          variables: z.record(z.any()).describe('Object with variable names and values'),
          instagramHandle: z.string().describe('The user\'s Instagram handle'),
          businessAnalysis: z.string().optional().describe('Summary of the business analysis')
        }),
        execute: async ({ botStyleId, variables, instagramHandle, businessAnalysis }) => {
          const params = { botStyleId, variables, instagramHandle, businessAnalysis };
          console.log('🔧 Executing save_onboarding_config tool with params:', params);
          // For now, save to user session or temporary storage
          // Organization will be created later with this data
          const user = authContext.session.user;

          // Store onboarding data temporarily (could use Redis, session, or temp DB table)
          // For now, return success without actual saving
          console.log('📋 Onboarding config to be saved later:', {
            userId: user.id,
            ...params
          });

          const result = JSON.stringify({
            success: true,
            message: 'Onboarding configuration prepared successfully! Next step: create organization.',
            ...params
          });
          console.log('✅ Save onboarding config tool completed');
          return result;
        }
      })
    };

    // Anthropic built-in web search tool (server-side tool use)
    const anthropicProviderTools = isAnthropic
      ? [
          {
            type: 'web_search_20250305',
            name: 'web_search',
            max_uses: 15
          }
        ]
      : undefined;

    // System prompt for onboarding
    const systemPrompt = `You are an AI onboarding assistant for an Instagram automation platform. Your goal is to help new users set up their Instagram automation by:

1. **Business Analysis**: Get their Instagram handle and analyze their business using web search
2. **Bot Style Selection**: Choose the best matching bot style from available options
3. **Variable Configuration**: Fill in all required variables based on gathered information
4. **Confirmation**: Present the configuration for user approval

## Available Tools:
- **web_search**: Use this to search for information about the user's Instagram profile and business. Search for their Instagram handle, business name, or industry to gather insights.
- **get_bot_styles**: Retrieve all available bot styles to choose the best match
- **get_bot_style_variables**: Get variables for a selected bot style that need to be configured
- **save_onboarding_config**: Save the final configuration

## Process Flow:
1. **Start**: Ask for Instagram handle if not provided
2. **Analyze**: Use web_search to gather business information about their Instagram profile and business
3. **Select**: Use get_bot_styles and choose the best match based on the analysis
4. **Configure**: Use get_bot_style_variables and fill them based on analysis
5. **Confirm**: Present configuration and ask for user confirmation
6. **Save**: Use save_onboarding_config to finalize setup

## Guidelines:
- Be conversational and friendly
- ALWAYS use web_search when you get an Instagram handle to analyze their business
- Explain your analysis and reasoning clearly
- Ask for clarification when information is unclear
- Present information clearly and concisely
- Always confirm before making final changes

Start by greeting the user and asking for their Instagram handle.`;

    // Stream the response
    const result = streamText({
      model,
      system: systemPrompt,
      messages: messages,
      tools,
      // For Anthropic, enable their native web_search tool.
      // The AI SDK will pass this through to Anthropic models that support it.
      ...(anthropicProviderTools ? { providerTools: anthropicProviderTools as any } : {}),
      stopWhen: stepCountIs(5), // Allow up to 5 tool execution steps
    });

    return result.toTextStreamResponse();

  } catch (error) {
    console.error('❌ Chat onboarding API error:', error);
    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Internal server error'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
