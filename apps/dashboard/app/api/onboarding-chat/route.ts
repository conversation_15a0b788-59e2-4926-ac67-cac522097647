import {
  convertToModelMessages,
  createUIMessageStream,
  JsonToSseTransformStream,
  smoothStream,
  stepCountIs,
  streamText,
} from 'ai';
import { createAnthropic } from '@ai-sdk/anthropic';
import { createOpenAI } from '@ai-sdk/openai';
import { NextRequest } from 'next/server';
import { z } from 'zod';

import { getAuthContext } from '@workspace/auth/context';
import { prisma } from '@workspace/database/client';
import { randomUUID } from 'crypto';

// Import our modern onboarding tools
import { webSearch } from '~/lib/ai/tools/onboarding/web-search';
import { getBotStyles } from '~/lib/ai/tools/onboarding/get-bot-styles';
import { getBotStyleVariables } from '~/lib/ai/tools/onboarding/get-bot-style-variables';
import { saveOnboardingConfig } from '~/lib/ai/tools/onboarding/save-onboarding-config';

export const maxDuration = 60;

const requestSchema = z.object({
  messages: z.array(z.object({
    id: z.string(),
    role: z.enum(['user', 'assistant']),
    content: z.string(),
  })),
});

export async function POST(req: NextRequest) {
  try {
    // Parse and validate request
    const body = await req.json();
    const { messages } = requestSchema.parse(body);

    // Get auth context
    const authContext = await getAuthContext();
    if (!authContext.session) {
      return new Response('Unauthorized', { status: 401 });
    }

    // Get platform settings for AI configuration
    const platformSettings = await prisma.platformSettings.findFirst();
    if (!platformSettings) {
      return new Response('Platform settings not configured', { status: 500 });
    }

    // Determine AI provider and model
    const selectedModel = platformSettings.aiModel || 'openai/gpt-4o-mini';
    const isAnthropic = selectedModel.includes('claude') || selectedModel.includes('anthropic');

    let model: any;
    if (isAnthropic) {
      if (!platformSettings.anthropicApiKey) {
        return new Response('Anthropic API key not configured', { status: 500 });
      }
      const anthropicClient = createAnthropic({
        apiKey: platformSettings.anthropicApiKey,
      });
      const modelName = selectedModel.replace('anthropic/', '').replace('claude/', '');
      model = anthropicClient(modelName);
    } else {
      if (!platformSettings.openRouterApiKey) {
        return new Response('OpenRouter API key not configured', { status: 500 });
      }
      const openaiClient = createOpenAI({
        apiKey: platformSettings.openRouterApiKey,
        baseURL: 'https://openrouter.ai/api/v1',
      });
      model = openaiClient(selectedModel.replace('openai/', ''));
    }

    // System prompt for onboarding
    const systemPrompt = `You are an AI onboarding assistant for an Instagram automation platform. Your goal is to help new users set up their Instagram automation through a conversational flow.

## Available Tools:
- **webSearch**: Search for information about Instagram profiles and businesses
- **getBotStyles**: Get available bot styles from the database
- **getBotStyleVariables**: Get variables for a specific bot style
- **saveOnboardingConfig**: Save the final configuration

## Process:
1. Ask for Instagram handle
2. Search and analyze their business
3. Select the best bot style
4. Configure variables based on analysis
5. Save configuration

Be conversational, helpful, and explain your reasoning clearly.`;

    // Create the modern streaming response
    const stream = createUIMessageStream({
      execute: ({ writer: dataStream }) => {
        const result = streamText({
          model,
          system: systemPrompt,
          messages: convertToModelMessages(messages),
          stopWhen: stepCountIs(10),
          experimental_activeTools: [
            'webSearch',
            'getBotStyles', 
            'getBotStyleVariables',
            'saveOnboardingConfig'
          ],
          experimental_transform: smoothStream({ chunking: 'word' }),
          tools: {
            webSearch: webSearch({ session: authContext.session, dataStream }),
            getBotStyles: getBotStyles({ session: authContext.session, dataStream }),
            getBotStyleVariables: getBotStyleVariables({ session: authContext.session, dataStream }),
            saveOnboardingConfig: saveOnboardingConfig({ session: authContext.session, dataStream }),
          },
        });

        result.consumeStream();

        dataStream.merge(
          result.toUIMessageStream({
            sendReasoning: true,
          }),
        );
      },
      generateId: randomUUID,
      onFinish: async ({ messages }) => {
        console.log('✅ Onboarding conversation completed');
        // Could save conversation history here if needed
      },
      onError: () => {
        return 'Sorry, there was an error during onboarding. Please try again.';
      },
    });

    return new Response(stream.pipeThrough(new JsonToSseTransformStream()));

  } catch (error) {
    console.error('❌ Onboarding chat API error:', error);
    return new Response(
      JSON.stringify({
        error: error instanceof Error ? error.message : 'Internal server error'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}
