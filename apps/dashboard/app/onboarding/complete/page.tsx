import * as React from 'react';
import { type Metadata } from 'next';
import { redirect } from 'next/navigation';
import { CheckCircle, ArrowRight } from 'lucide-react';

import { getAuthContext } from '@workspace/auth/context';
import { routes } from '@workspace/routes';
import { Logo } from '@workspace/ui/components/logo';
import { Button } from '@workspace/ui/components/button';

import { createTitle } from '~/lib/formatters';

export const metadata: Metadata = {
  title: createTitle('Onboarding Complete')
};

export default async function OnboardingCompletePage(): Promise<React.JSX.Element> {
  const ctx = await getAuthContext();

  if (ctx.session.user.completedOnboarding) {
    return redirect(routes.dashboard.organizations.Index);
  }

  return (
    <div className="relative min-h-screen flex flex-col bg-gradient-to-br from-blue-50/50 via-white to-purple-50/50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      {/* Header */}
      <div className="absolute inset-x-0 top-0 z-10 flex items-center justify-center p-4">
        <Logo />
      </div>

      {/* Success Content */}
      <div className="flex-1 flex items-center justify-center px-4">
        <div className="max-w-md w-full text-center">
          <div className="mb-8">
            <CheckCircle className="w-16 h-16 text-emerald-500 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              Welcome aboard! 🎉
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Your Instagram onboarding is complete. Let's start building your audience!
            </p>
          </div>

          <Button
            className="w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white"
            size="lg"
          >
            Get Started
            <ArrowRight className="w-4 h-4 ml-2" />
          </Button>
        </div>
      </div>
    </div>
  );
}