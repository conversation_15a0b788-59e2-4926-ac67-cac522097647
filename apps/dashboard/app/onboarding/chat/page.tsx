import * as React from 'react';
import { type Metadata } from 'next';
import { redirect } from 'next/navigation';
import { ChevronLeftIcon } from 'lucide-react';

import { getAuthContext } from '@workspace/auth/context';
import { routes } from '@workspace/routes';
import { Logo } from '@workspace/ui/components/logo';

import { ChatInterface } from '~/components/onboarding/chat/chat-interface';
import { SignOutButton } from '~/components/onboarding/sign-out-button';
import { createTitle } from '~/lib/formatters';

export const metadata: Metadata = {
  title: createTitle('Chat Onboarding')
};

export default async function ChatOnboardingPage(): Promise<React.JSX.Element> {
  const ctx = await getAuthContext();

  if (ctx.session.user.completedOnboarding) {
    return redirect(routes.dashboard.organizations.Index);
  }

  return (
    <div className="relative min-h-screen bg-gradient-to-br from-indigo-100 via-purple-50 to-pink-100 dark:from-gray-900 dark:via-purple-900/20 dark:to-indigo-900/20">
      {/* Floating Header */}
      <div className="absolute inset-x-0 top-0 z-10 flex items-center justify-center p-6">
        <div className="bg-white/80 dark:bg-white/10 backdrop-blur-md rounded-full px-6 py-3 shadow-lg border border-white/20">
          <Logo />
        </div>
      </div>

      {/* Floating Sign Out */}
      <SignOutButton
        type="button"
        variant="ghost"
        className="absolute left-6 top-6 z-10 text-gray-600 hover:text-gray-900 dark:text-gray-300 dark:hover:text-white bg-white/80 dark:bg-white/10 backdrop-blur-md rounded-full px-4 py-2 shadow-lg border border-white/20 hover:bg-white/90 dark:hover:bg-white/20 transition-all duration-200"
      >
        <ChevronLeftIcon className="mr-2 size-4 shrink-0" />
        Sign out
      </SignOutButton>

      {/* Full Screen Chat Interface */}
      <div className="h-screen">
        <ChatInterface />
      </div>
    </div>
  );
}