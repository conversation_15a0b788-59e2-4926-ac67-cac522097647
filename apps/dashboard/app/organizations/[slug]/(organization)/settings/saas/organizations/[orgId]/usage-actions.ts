'use server';

import { redirect } from 'next/navigation';
import type { ReadonlyURLSearchParams } from 'next/navigation';
import { replaceOrgSlug, routes } from '@workspace/routes';

/**
 * Filter by date range for organization usage
 */
export async function filterByDateRange(
  orgSlug: string,
  orgId: string,
  dateFrom: string | null,
  dateTo: string | null,
  preset?: string | null,
  searchParams?: ReadonlyURLSearchParams
): Promise<void> {
  const params = searchParams ? new URLSearchParams(searchParams) : new URLSearchParams();

  // Set preset if provided
  if (preset && preset !== 'custom') {
    params.set('preset', preset);
    params.delete('from');
    params.delete('to');
  } else if (preset === 'custom') {
    // When switching to custom, keep existing custom dates if they exist
    params.set('preset', 'custom');
    // Don't delete from/to params - let user set them via date picker
  } else {
    // Set custom date range (when applying custom dates)
    params.delete('preset');
    if (dateFrom) {
      params.set('from', dateFrom);
    } else {
      params.delete('from');
    }
    if (dateTo) {
      params.set('to', dateTo);
    } else {
      params.delete('to');
    }
  }

  const baseUrl = replaceOrgSlug(routes.dashboard.organizations.slug.settings.saas.OrganizationDetails, orgSlug)
    .replace('[orgId]', orgId);
  const newUrl = `${baseUrl}?${params.toString()}`;
  redirect(newUrl);
}

/**
 * Clear all filters and reset to default state
 */
export async function clearFilters(
  orgSlug: string,
  orgId: string
): Promise<void> {
  const newUrl = replaceOrgSlug(routes.dashboard.organizations.slug.settings.saas.OrganizationDetails, orgSlug)
    .replace('[orgId]', orgId);
  redirect(newUrl);
}