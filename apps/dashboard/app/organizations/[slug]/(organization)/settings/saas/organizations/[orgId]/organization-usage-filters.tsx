'use client';

import * as React from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import { CalendarIcon, XIcon } from 'lucide-react';
import { useFormStatus } from 'react-dom';
import { format } from 'date-fns';

import { Button } from '@workspace/ui/components/button';
import { Calendar } from '@workspace/ui/components/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@workspace/ui/components/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@workspace/ui/components/select';
import { cn } from '@workspace/ui/lib/utils';

import { filterByDateRange, clearFilters } from './usage-actions';
import { getDateRangeFromPreset } from './organization-usage-search-params';

export function OrganizationUsageFilters(): React.JSX.Element {
  const params = useParams();
  const searchParams = useSearchParams();
  const orgSlug = params.slug as string;
  const orgId = params.orgId as string;
  const { pending } = useFormStatus();

  const [dateFrom, setDateFrom] = React.useState<Date | undefined>();
  const [dateTo, setDateTo] = React.useState<Date | undefined>();

  // Parse current filter values
  const currentPreset = searchParams.get('preset') || 'month';
  const customDateFrom = searchParams.get('from');
  const customDateTo = searchParams.get('to');

  // Initialize date range based on current params
  React.useEffect(() => {
    if (customDateFrom && customDateTo) {
      setDateFrom(new Date(customDateFrom));
      setDateTo(new Date(customDateTo));
    } else {
      const { from, to } = getDateRangeFromPreset(currentPreset);
      setDateFrom(from);
      setDateTo(to);
    }
  }, [currentPreset, customDateFrom, customDateTo]);

  const handlePresetChange = async (preset: string) => {
    await filterByDateRange(orgSlug, orgId, null, null, preset, searchParams);
  };

  const handleCustomDateRangeChange = async () => {
    if (dateFrom && dateTo) {
      await filterByDateRange(
        orgSlug,
        orgId,
        dateFrom.toISOString(),
        dateTo.toISOString(),
        null,
        searchParams
      );
    }
  };

  const handleClearFilters = async () => {
    await clearFilters(orgSlug, orgId);
  };

  const hasActiveFilters = searchParams.get('preset') !== 'month' ||
    searchParams.get('from') ||
    searchParams.get('to');

  return (
    <div className="flex items-center gap-2">
      {/* Date Range Preset Selector */}
      <Select value={currentPreset} onValueChange={handlePresetChange}>
        <SelectTrigger className="w-[140px]">
          <SelectValue placeholder="Time period" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="day">Today</SelectItem>
          <SelectItem value="week">Last 7 days</SelectItem>
          <SelectItem value="month">This month</SelectItem>
          <SelectItem value="custom">Custom range</SelectItem>
        </SelectContent>
      </Select>

      {/* Custom Date Range Picker */}
      {currentPreset === 'custom' && (
        <div className="flex items-center gap-2">
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-[120px] justify-start text-left font-normal",
                  !dateFrom && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateFrom ? format(dateFrom, "MMM dd") : "From"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={dateFrom}
                onSelect={setDateFrom}
                initialFocus
              />
            </PopoverContent>
          </Popover>

          <span className="text-muted-foreground">to</span>

          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-[120px] justify-start text-left font-normal",
                  !dateTo && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {dateTo ? format(dateTo, "MMM dd") : "To"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={dateTo}
                onSelect={setDateTo}
                initialFocus
              />
            </PopoverContent>
          </Popover>

          <Button
            onClick={handleCustomDateRangeChange}
            size="sm"
            disabled={!dateFrom || !dateTo || pending}
          >
            Apply
          </Button>
        </div>
      )}

      {/* Clear Filters */}
      {hasActiveFilters && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleClearFilters}
          disabled={pending}
        >
          <XIcon className="h-4 w-4 mr-1" />
          Clear
        </Button>
      )}
    </div>
  );
}