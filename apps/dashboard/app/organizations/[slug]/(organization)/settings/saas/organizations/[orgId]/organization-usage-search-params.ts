import { createSearchParamsCache, parseAsString } from 'nuqs/server';

export const organizationUsageSearchParamsCache = createSearchParamsCache({
  // Date filtering
  preset: parseAsString.withDefault('month'), // 'day', 'week', 'month', 'custom'
  from: parseAsString, // ISO date string
  to: parseAsString,   // ISO date string
});

// Helper function to convert preset to actual dates
export function getDateRangeFromPreset(preset: string): { from: Date; to: Date } {
  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

  switch (preset) {
    case 'day':
      return {
        from: new Date(today),
        to: new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1) // End of day
      };

    case 'week':
      const weekStart = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
      return {
        from: weekStart,
        to: new Date(today.getTime() + 24 * 60 * 60 * 1000 - 1)
      };

    case 'month':
    default:
      return {
        from: new Date(now.getFullYear(), now.getMonth(), 1),
        to: new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59)
      };
  }
}