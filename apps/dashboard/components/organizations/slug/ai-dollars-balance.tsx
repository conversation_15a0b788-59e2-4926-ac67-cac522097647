'use client';

import * as React from 'react';
import { Card, CardContent } from '@workspace/ui/components/card';

export interface AIDollarsBalanceProps {
  subscriptionTier: string;
  pricePerRequest: number;
  balance: number;
  dailyUsageRate: number;
}

export function AIDollarsBalance({
  subscriptionTier,
  pricePerRequest,
  balance,
  dailyUsageRate,
}: AIDollarsBalanceProps): React.JSX.Element {
  const balanceFormatted = balance.toFixed(2);
  const dailyUsageCost = dailyUsageRate * pricePerRequest;
  const dailyUsageFormatted = dailyUsageCost.toFixed(2);

  return (
    <Card className="mx-3 mb-0 py-0">
      <CardContent className="p-2 text-sm">
        <div className="flex flex-col space-y-2">
          <div className="text-xs text-muted-foreground">
            Avg. daily usage (last 30 days): <span className="text-white font-bold">${dailyUsageFormatted}</span>
          </div>
          <div className="flex items-center justify-start space-x-2">
            <span className="text-xs text-muted-foreground">Amount</span>
            <span className="font-medium">${balanceFormatted}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}