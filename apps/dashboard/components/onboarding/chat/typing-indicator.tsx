'use client';

import { motion } from 'framer-motion';
import { Bo<PERSON> } from 'lucide-react';

export function TypingIndicator() {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20, scale: 0.8 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      exit={{ opacity: 0, y: -20, scale: 0.8 }}
      transition={{ duration: 0.4, type: "spring" }}
      className="flex gap-4 mb-8"
    >
      {/* Avatar with breathing animation */}
      <motion.div
        animate={{
          scale: [1, 1.05, 1],
          boxShadow: [
            "0 10px 25px rgba(16, 185, 129, 0.3)",
            "0 15px 35px rgba(20, 184, 166, 0.4)",
            "0 10px 25px rgba(16, 185, 129, 0.3)"
          ]
        }}
        transition={{
          duration: 2,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        className="flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center shadow-xl ring-2 ring-white/50 bg-gradient-to-br from-emerald-400 via-teal-500 to-cyan-600"
      >
        <Bot className="w-6 h-6 text-white drop-shadow-sm" />
      </motion.div>

      {/* Enhanced Typing Bubble */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8, x: -50 }}
        animate={{ opacity: 1, scale: 1, x: 0 }}
        transition={{ duration: 0.4, type: "spring" }}
        className="bg-white/90 dark:bg-white/10 backdrop-blur-sm border border-white/30 px-6 py-4 rounded-3xl shadow-lg shadow-teal-500/10"
      >
        <div className="flex items-center gap-1">
          <div className="flex space-x-1.5">
            <motion.div
              className="w-3 h-3 bg-gradient-to-r from-emerald-400 to-teal-500 rounded-full"
              animate={{
                y: [0, -12, 0],
                scale: [1, 1.2, 1],
                opacity: [0.7, 1, 0.7]
              }}
              transition={{
                duration: 0.8,
                repeat: Infinity,
                delay: 0
              }}
            />
            <motion.div
              className="w-3 h-3 bg-gradient-to-r from-teal-400 to-cyan-500 rounded-full"
              animate={{
                y: [0, -12, 0],
                scale: [1, 1.2, 1],
                opacity: [0.7, 1, 0.7]
              }}
              transition={{
                duration: 0.8,
                repeat: Infinity,
                delay: 0.2
              }}
            />
            <motion.div
              className="w-3 h-3 bg-gradient-to-r from-cyan-400 to-blue-500 rounded-full"
              animate={{
                y: [0, -12, 0],
                scale: [1, 1.2, 1],
                opacity: [0.7, 1, 0.7]
              }}
              transition={{
                duration: 0.8,
                repeat: Infinity,
                delay: 0.4
              }}
            />
          </div>
          <span className="text-xs text-gray-500 dark:text-gray-400 ml-2 font-medium">
            AI is thinking...
          </span>
        </div>
      </motion.div>
    </motion.div>
  );
}