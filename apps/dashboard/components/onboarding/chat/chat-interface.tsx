'use client';

import { useEffect, useRef, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChatMessage } from './chat-message';
import { ChatInput } from './chat-input';
import { TypingIndicator } from './typing-indicator';

interface ChatInterfaceProps {
  initialMessage?: string;
}

export function ChatInterface({
  initialMessage = "Hello There! 👋\n\nPlease give me your Instagram handle so I can ask you a few questions"
}: ChatInterfaceProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [displayMessages, setDisplayMessages] = useState<Array<{
    id: string;
    role: 'user' | 'assistant';
    content: string;
  }>>([
    {
      id: 'initial',
      role: 'assistant',
      content: initialMessage
    }
  ]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [displayMessages, isLoading]);


  const handleSendMessage = async (content: string) => {
    if (!content.trim() || isLoading) return;

    setInput('');
    setIsLoading(true);

    // Add user message
    const newUserMessage = {
      id: `user-${Date.now()}`,
      role: 'user' as const,
      content: content.trim()
    };

    setDisplayMessages(prev => [...prev, newUserMessage]);

    try {
      // Call API
      const response = await fetch('/api/chat-onboarding', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          messages: [...displayMessages, newUserMessage].map(m => ({
            role: m.role,
            content: m.content
          }))
        })
      });

      if (!response.ok) {
        throw new Error('Failed to get response');
      }

      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('No response body');
      }

      let assistantMessage = '';
      const assistantMessageId = `assistant-${Date.now()}`;

      // Add empty assistant message
      setDisplayMessages(prev => [...prev, {
        id: assistantMessageId,
        role: 'assistant' as const,
        content: ''
      }]);

      // Read the stream
      const decoder = new TextDecoder();
      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          assistantMessage += chunk;

          // Update the assistant message
          setDisplayMessages(prev => prev.map(m =>
            m.id === assistantMessageId
              ? { ...m, content: assistantMessage }
              : m
          ));
        }
      } finally {
        reader.releaseLock();
      }

    } catch (error) {
      console.error('Chat error:', error);
      setDisplayMessages(prev => [...prev, {
        id: `error-${Date.now()}`,
        role: 'assistant' as const,
        content: 'Sorry, I encountered an error. Please try again.'
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-full relative">
      {/* Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-violet-100/50 via-pink-50/50 to-cyan-100/50 dark:from-violet-900/20 dark:via-pink-900/20 dark:to-cyan-900/20">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.8)_1px,transparent_0)] bg-[size:20px_20px] dark:bg-[radial-gradient(circle_at_1px_1px,rgba(255,255,255,0.1)_1px,transparent_0)]" />
      </div>

      {/* Welcome Animation */}
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.6 }}
        className="relative z-10 flex flex-col h-full"
      >
        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto px-4 pt-24 pb-6">
          <div className="max-w-3xl mx-auto">
            <AnimatePresence>
              {displayMessages.map((message, index) => (
                <motion.div
                  key={message.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.4, delay: index * 0.1 }}
                >
                  <ChatMessage
                    message={{
                      id: message.id,
                      role: message.role,
                      content: message.content
                    }}
                    isLast={index === displayMessages.length - 1}
                  />
                </motion.div>
              ))}
              {isLoading && <TypingIndicator />}
            </AnimatePresence>
            <div ref={messagesEndRef} />
          </div>
        </div>

        {/* Floating Input Area */}
        <div className="relative z-20">
          <div className="max-w-3xl mx-auto px-4 pb-6">
            <ChatInput
              onSendMessage={handleSendMessage}
              disabled={isLoading}
              placeholder="Type your response..."
              value={input}
              onChange={(e) => setInput(e.target.value)}
            />
          </div>
        </div>
      </motion.div>
    </div>
  );
}