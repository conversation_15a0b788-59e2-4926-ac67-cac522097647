'use client';

import { motion } from 'framer-motion';
import { Bot, User } from 'lucide-react';
import { cn } from '@workspace/ui/lib/utils';

interface ChatMessageProps {
  message: {
    id: string;
    role: 'user' | 'assistant';
    content: string;
  };
  isLast?: boolean;
}

export function ChatMessage({ message, isLast }: ChatMessageProps) {
  const isUser = message.role === 'user';

  return (
    <div
      className={cn(
        'flex gap-4 mb-8',
        isUser ? 'flex-row-reverse' : 'flex-row'
      )}
    >
      {/* Avatar */}
      <motion.div
        initial={{ scale: 0, rotate: -180 }}
        animate={{ scale: 1, rotate: 0 }}
        transition={{ duration: 0.4, type: "spring" }}
        className={cn(
          'flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center shadow-xl ring-2 ring-white/50',
          isUser
            ? 'bg-gradient-to-br from-violet-500 via-purple-500 to-pink-500'
            : 'bg-gradient-to-br from-emerald-400 via-teal-500 to-cyan-600'
        )}
      >
        {isUser ? (
          <User className="w-6 h-6 text-white drop-shadow-sm" />
        ) : (
          <Bot className="w-6 h-6 text-white drop-shadow-sm" />
        )}
      </motion.div>

      {/* Message Bubble */}
      <motion.div
        initial={{ opacity: 0, scale: 0.8, x: isUser ? 50 : -50 }}
        animate={{ opacity: 1, scale: 1, x: 0 }}
        transition={{ duration: 0.4, type: "spring" }}
        className={cn(
          'max-w-[75%] px-6 py-4 rounded-3xl shadow-lg backdrop-blur-sm border',
          isUser
            ? 'bg-gradient-to-br from-violet-500 via-purple-500 to-pink-500 text-white border-white/20 ml-auto shadow-violet-500/25'
            : 'bg-white/90 dark:bg-white/10 text-gray-800 dark:text-gray-100 border-white/30 shadow-teal-500/10'
        )}
      >
        <p className="text-base leading-relaxed whitespace-pre-wrap font-medium">
          {message.content}
        </p>

        {/* Message timestamp */}
        <div className={cn(
          'text-xs mt-2 opacity-70',
          isUser ? 'text-white/80' : 'text-gray-500'
        )}>
          Just now
        </div>
      </motion.div>
    </div>
  );
}