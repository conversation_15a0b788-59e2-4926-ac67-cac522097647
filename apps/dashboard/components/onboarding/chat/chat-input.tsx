'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { Send, Loader2 } from 'lucide-react';
import { Button } from '@workspace/ui/components/button';
import { Input } from '@workspace/ui/components/input';
import { cn } from '@workspace/ui/lib/utils';

interface ChatInputProps {
  onSendMessage?: (message: string) => void;
  disabled?: boolean;
  placeholder?: string;
  value?: string;
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export function ChatInput({
  onSendMessage,
  disabled = false,
  placeholder = 'Type your message...',
  value,
  onChange
}: ChatInputProps) {
  const [localInput, setLocalInput] = useState('');
  const [isSending, setIsSending] = useState(false);

  // Use controlled or uncontrolled input
  const inputValue = value !== undefined ? value : localInput;
  const handleInputChange = onChange || ((e) => setLocalInput(e.target.value));

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim() || disabled || isSending) return;

    if (onSendMessage) {
      const message = inputValue.trim();
      if (value === undefined) {
        setLocalInput('');
      }
      setIsSending(true);

      try {
        await onSendMessage(message);
      } finally {
        setIsSending(false);
      }
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e as any);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 30, scale: 0.95 }}
      animate={{ opacity: 1, y: 0, scale: 1 }}
      transition={{ duration: 0.4, delay: 0.2, type: "spring" }}
      className="bg-white/70 dark:bg-white/5 backdrop-blur-xl rounded-3xl shadow-2xl border border-white/30 p-4 mx-4"
    >
      <form onSubmit={handleSubmit} className="flex items-center gap-4">
        <div className="flex-1 relative">
          <Input
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder={placeholder}
            disabled={disabled || isSending}
            className={cn(
              'bg-transparent border-none shadow-none text-lg placeholder:text-gray-500/70',
              'focus:ring-0 focus:border-transparent',
              'transition-all duration-200 font-medium',
              'text-gray-800 dark:text-gray-100',
              'py-4 px-0'
            )}
          />

          {/* Animated Border */}
          <motion.div
            className="absolute bottom-0 left-0 h-0.5 bg-gradient-to-r from-violet-500 to-pink-500"
            initial={{ width: 0 }}
            animate={{ width: inputValue.length > 0 ? '100%' : '0%' }}
            transition={{ duration: 0.3 }}
          />
        </div>

        <motion.div
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <Button
            type="submit"
            disabled={!inputValue.trim() || disabled || isSending}
            size="lg"
            className={cn(
              'bg-gradient-to-r from-violet-500 via-purple-500 to-pink-500',
              'hover:from-violet-600 hover:via-purple-600 hover:to-pink-600',
              'text-white border-none shadow-xl shadow-violet-500/25',
              'transition-all duration-300 transform rounded-2xl',
              'disabled:opacity-50 disabled:cursor-not-allowed disabled:shadow-none',
              'w-12 h-12 p-0'
            )}
          >
            <motion.div
              animate={{ rotate: isSending ? 360 : 0 }}
              transition={{ duration: 0.5, repeat: isSending ? Infinity : 0, ease: "linear" }}
            >
              {isSending ? (
                <Loader2 className="w-5 h-5" />
              ) : (
                <Send className="w-5 h-5" />
              )}
            </motion.div>
          </Button>
        </motion.div>
      </form>
    </motion.div>
  );
}