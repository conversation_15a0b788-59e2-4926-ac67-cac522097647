'use client';

import { useChat } from 'ai/react';
import { useState } from 'react';
import { Button } from '@workspace/ui/components/button';
import { Input } from '@workspace/ui/components/input';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@workspace/ui/components/card';
import { Badge } from '@workspace/ui/components/badge';
import { Loader2, Search, Bot, Settings, Save } from 'lucide-react';

interface SearchStatus {
  status: 'searching' | 'completed' | 'error';
  query: string;
  focus?: string;
  error?: string;
}

interface BotStylesData {
  botStyles: Array<{
    id: string;
    name: string;
    description: string;
    category: string;
  }>;
}

interface VariablesData {
  botStyle: {
    id: string;
    name: string;
    description: string;
  };
  variables: Array<{
    id: string;
    name: string;
    type: string;
    description?: string;
    defaultValue?: string;
    isRequired: boolean;
  }>;
}

export function ModernOnboardingChat() {
  const [searchStatus, setSearchStatus] = useState<SearchStatus | null>(null);
  const [botStyles, setBotStyles] = useState<BotStylesData | null>(null);
  const [variables, setVariables] = useState<VariablesData | null>(null);
  const [saveStatus, setSaveStatus] = useState<'saving' | 'completed' | 'error' | null>(null);

  const { messages, input, handleInputChange, handleSubmit, isLoading, data } = useChat({
    api: '/api/onboarding-chat',
    onFinish: () => {
      console.log('✅ Chat finished');
    },
    experimental_onToolCall: ({ toolCall }) => {
      console.log('🔧 Tool called:', toolCall.toolName, toolCall.args);
    },
    experimental_onFunctionCall: ({ functionCall }) => {
      console.log('🔧 Function called:', functionCall.name, functionCall.arguments);
    },
    onError: (error) => {
      console.error('❌ Chat error:', error);
    },
  });

  // Handle streaming data updates
  if (data) {
    data.forEach((item: any) => {
      switch (item.type) {
        case 'data-search-status':
          setSearchStatus(item.data);
          break;
        case 'data-bot-styles':
          setBotStyles(item.data);
          break;
        case 'data-variables':
          setVariables(item.data);
          break;
        case 'data-save-status':
          setSaveStatus(item.data.status);
          break;
      }
    });
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Bot className="h-6 w-6" />
            Instagram Automation Onboarding
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Status Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Search Status */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Search className="h-4 w-4" />
              <span className="font-medium">Web Search</span>
              {searchStatus && (
                <Badge variant={
                  searchStatus.status === 'searching' ? 'secondary' :
                  searchStatus.status === 'completed' ? 'default' : 'destructive'
                }>
                  {searchStatus.status === 'searching' && <Loader2 className="h-3 w-3 animate-spin mr-1" />}
                  {searchStatus.status}
                </Badge>
              )}
            </div>
            {searchStatus?.query && (
              <p className="text-sm text-muted-foreground mt-1">
                Searching: {searchStatus.query}
              </p>
            )}
          </CardContent>
        </Card>

        {/* Bot Styles Status */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              <span className="font-medium">Bot Styles</span>
              {botStyles && (
                <Badge variant="default">
                  {botStyles.botStyles.length} found
                </Badge>
              )}
            </div>
            {variables && (
              <p className="text-sm text-muted-foreground mt-1">
                Selected: {variables.botStyle.name}
              </p>
            )}
          </CardContent>
        </Card>

        {/* Save Status */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center gap-2">
              <Save className="h-4 w-4" />
              <span className="font-medium">Configuration</span>
              {saveStatus && (
                <Badge variant={
                  saveStatus === 'saving' ? 'secondary' :
                  saveStatus === 'completed' ? 'default' : 'destructive'
                }>
                  {saveStatus === 'saving' && <Loader2 className="h-3 w-3 animate-spin mr-1" />}
                  {saveStatus}
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Variables Display */}
      {variables && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg">
              {variables.botStyle.name} - Configuration Variables
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {variables.variables.map((variable) => (
                <div key={variable.id} className="p-3 border rounded-lg">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="font-medium">{variable.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {variable.type}
                    </Badge>
                    {variable.isRequired && (
                      <Badge variant="destructive" className="text-xs">
                        Required
                      </Badge>
                    )}
                  </div>
                  {variable.description && (
                    <p className="text-sm text-muted-foreground mb-1">
                      {variable.description}
                    </p>
                  )}
                  {variable.defaultValue && (
                    <p className="text-xs text-muted-foreground">
                      Default: {variable.defaultValue}
                    </p>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Chat Messages */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Conversation</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {messages.length === 0 && (
            <div className="text-center text-muted-foreground py-8">
              <Bot className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Hello! I'm here to help you set up your Instagram automation.</p>
              <p className="text-sm mt-2">Please provide your Instagram handle to get started.</p>
            </div>
          )}
          
          {messages.map((message) => (
            <div
              key={message.id}
              className={`flex ${message.role === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              <div
                className={`max-w-[80%] p-3 rounded-lg ${
                  message.role === 'user'
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted'
                }`}
              >
                <div className="whitespace-pre-wrap">{message.content}</div>
              </div>
            </div>
          ))}
          
          {isLoading && (
            <div className="flex justify-start">
              <div className="bg-muted p-3 rounded-lg">
                <Loader2 className="h-4 w-4 animate-spin" />
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Input Form */}
      <Card>
        <CardContent className="p-4">
          <form onSubmit={handleSubmit} className="flex gap-2">
            <Input
              value={input}
              onChange={handleInputChange}
              placeholder="Type your message..."
              disabled={isLoading}
              className="flex-1"
            />
            <Button type="submit" disabled={isLoading || !input.trim()}>
              {isLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                'Send'
              )}
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  );
}
