import { tool, type UIMessageStreamWriter } from 'ai';
import { z } from 'zod';
import type { Session } from 'next-auth';
import { prisma } from '@workspace/database/client';

interface GetBotStylesProps {
  session: Session;
  dataStream: UIMessageStreamWriter<any>;
}

export const getBotStyles = ({ session, dataStream }: GetBotStylesProps) =>
  tool({
    description: 'Get all available bot styles from the database',
    inputSchema: z.object({}),
    execute: async () => {
      try {
        console.log('📋 Fetching all bot styles from database');

        // Send loading status to UI
        dataStream.write({
          type: 'data-bot-styles-status',
          data: { status: 'loading' },
          transient: true,
        });

        // Fetch bot styles from database
        const botStyles = await prisma.botStyle.findMany({
          select: {
            id: true,
            name: true,
            description: true,
            category: true,
            isActive: true,
          },
          where: {
            isActive: true,
          },
          orderBy: {
            name: 'asc',
          },
        });

        console.log(`✅ Found ${botStyles.length} bot styles`);

        // Send bot styles to UI
        dataStream.write({
          type: 'data-bot-styles',
          data: { botStyles },
          transient: true,
        });

        // Send completion status to UI
        dataStream.write({
          type: 'data-bot-styles-status',
          data: { status: 'completed', count: botStyles.length },
          transient: true,
        });

        // Format for Claude's understanding
        const stylesDescription = botStyles.map(style => 
          `- **${style.name}** (${style.category}): ${style.description}`
        ).join('\n');

        return `Found ${botStyles.length} available bot styles:

${stylesDescription}

These are the available automation styles. I'll help you choose the best one based on your business analysis.`;

      } catch (error) {
        console.error('❌ Error fetching bot styles:', error);

        // Send error status to UI
        dataStream.write({
          type: 'data-bot-styles-status',
          data: { status: 'error', error: error instanceof Error ? error.message : 'Unknown error' },
          transient: true,
        });

        return 'I encountered an issue fetching the available bot styles. Let me try again or we can proceed with manual configuration.';
      }
    },
  });
