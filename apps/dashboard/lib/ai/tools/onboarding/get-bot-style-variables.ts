import { tool, type UIMessageStreamWriter } from 'ai';
import { z } from 'zod';
import type { Session } from 'next-auth';
import { prisma } from '@workspace/database/client';

interface GetBotStyleVariablesProps {
  session: Session;
  dataStream: UIMessageStreamWriter<any>;
}

export const getBotStyleVariables = ({ session, dataStream }: GetBotStyleVariablesProps) =>
  tool({
    description: 'Get variables for a specific bot style',
    inputSchema: z.object({
      botStyleId: z.string().describe('The ID of the bot style to get variables for'),
    }),
    execute: async ({ botStyleId }) => {
      try {
        console.log(`📝 Fetching variables for bot style: ${botStyleId}`);

        // Send loading status to UI
        dataStream.write({
          type: 'data-variables-status',
          data: { status: 'loading', botStyleId },
          transient: true,
        });

        // Fetch bot style and its variables
        const botStyle = await prisma.botStyle.findUnique({
          where: { id: botStyleId },
          include: {
            variables: {
              orderBy: {
                order: 'asc',
              },
            },
          },
        });

        if (!botStyle) {
          throw new Error(`Bot style with ID ${botStyleId} not found`);
        }

        console.log(`✅ Found ${botStyle.variables.length} variables for bot style: ${botStyle.name}`);

        // Send variables to UI
        dataStream.write({
          type: 'data-variables',
          data: { 
            botStyle: {
              id: botStyle.id,
              name: botStyle.name,
              description: botStyle.description,
            },
            variables: botStyle.variables 
          },
          transient: true,
        });

        // Send completion status to UI
        dataStream.write({
          type: 'data-variables-status',
          data: { status: 'completed', botStyleId, count: botStyle.variables.length },
          transient: true,
        });

        // Format variables for Claude's understanding
        const variablesDescription = botStyle.variables.map(variable => {
          let description = `- **${variable.name}** (${variable.type})`;
          if (variable.description) {
            description += `: ${variable.description}`;
          }
          if (variable.defaultValue) {
            description += ` [Default: ${variable.defaultValue}]`;
          }
          if (variable.isRequired) {
            description += ` *Required*`;
          }
          return description;
        }).join('\n');

        return `Found ${botStyle.variables.length} variables for **${botStyle.name}**:

${variablesDescription}

Now I need to help you configure these variables based on your business information. Let me analyze what we know about your business and suggest appropriate values for each variable.`;

      } catch (error) {
        console.error('❌ Error fetching bot style variables:', error);

        // Send error status to UI
        dataStream.write({
          type: 'data-variables-status',
          data: { 
            status: 'error', 
            botStyleId, 
            error: error instanceof Error ? error.message : 'Unknown error' 
          },
          transient: true,
        });

        return `I encountered an issue fetching the variables for the selected bot style. Error: ${error instanceof Error ? error.message : 'Unknown error'}`;
      }
    },
  });
