import { tool, type UIMessageStreamWriter } from 'ai';
import { z } from 'zod';
import type { Session } from 'next-auth';
import { prisma } from '@workspace/database/client';

interface SaveOnboardingConfigProps {
  session: Session;
  dataStream: UIMessageStreamWriter<any>;
}

export const saveOnboardingConfig = ({ session, dataStream }: SaveOnboardingConfigProps) =>
  tool({
    description: 'Save the final onboarding configuration to the database',
    inputSchema: z.object({
      instagramHandle: z.string().describe('The Instagram handle'),
      botStyleId: z.string().describe('The selected bot style ID'),
      variableValues: z.record(z.string(), z.any()).describe('The configured variable values'),
      businessInfo: z.object({
        name: z.string().optional(),
        industry: z.string().optional(),
        description: z.string().optional(),
        targetAudience: z.string().optional(),
        goals: z.string().optional(),
      }).optional().describe('Business information gathered during onboarding'),
    }),
    execute: async ({ instagramHandle, botStyleId, variableValues, businessInfo }) => {
      try {
        console.log('💾 Saving onboarding configuration...');

        // Send saving status to UI
        dataStream.write({
          type: 'data-save-status',
          data: { status: 'saving' },
          transient: true,
        });

        // Get the user's organization
        const user = await prisma.user.findUnique({
          where: { id: session.user.id },
          include: {
            memberships: {
              include: {
                organization: true,
              },
            },
          },
        });

        if (!user || user.memberships.length === 0) {
          throw new Error('User organization not found');
        }

        const organizationId = user.memberships[0].organizationId;

        // Save the onboarding configuration
        const onboardingConfig = await prisma.onboardingConfig.create({
          data: {
            userId: session.user.id,
            organizationId,
            instagramHandle,
            botStyleId,
            variableValues: variableValues as any, // Prisma Json type
            businessInfo: businessInfo as any, // Prisma Json type
            isCompleted: true,
            completedAt: new Date(),
          },
        });

        console.log('✅ Onboarding configuration saved successfully');

        // Send success status to UI
        dataStream.write({
          type: 'data-save-status',
          data: { status: 'completed', configId: onboardingConfig.id },
          transient: true,
        });

        // Send final configuration to UI
        dataStream.write({
          type: 'data-final-config',
          data: {
            id: onboardingConfig.id,
            instagramHandle,
            botStyleId,
            variableValues,
            businessInfo,
          },
          transient: true,
        });

        return `🎉 **Onboarding Complete!**

Your Instagram automation has been successfully configured:

**Instagram Handle:** @${instagramHandle}
**Bot Style:** Selected and configured
**Variables:** ${Object.keys(variableValues).length} variables configured
**Business Info:** ${businessInfo ? 'Captured and saved' : 'Basic setup'}

Your automation is now ready to use! You can:
1. Test your bot configuration
2. Activate automation for your Instagram account
3. Monitor performance in the dashboard
4. Adjust settings anytime

Welcome to AIChromaPRO! 🚀`;

      } catch (error) {
        console.error('❌ Error saving onboarding configuration:', error);

        // Send error status to UI
        dataStream.write({
          type: 'data-save-status',
          data: { 
            status: 'error', 
            error: error instanceof Error ? error.message : 'Unknown error' 
          },
          transient: true,
        });

        return `I encountered an issue saving your configuration: ${error instanceof Error ? error.message : 'Unknown error'}. Please try again or contact support.`;
      }
    },
  });
