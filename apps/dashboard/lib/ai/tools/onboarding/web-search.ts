import { tool } from 'ai';
import { z } from 'zod';
import type { Session } from 'next-auth';
import { createAnthropic } from '@ai-sdk/anthropic';
import { prisma } from '@workspace/database/client';

interface WebSearchProps {
  session: Session;
}

export const webSearch = ({ session }: WebSearchProps) =>
  tool({
    description: 'Search the web for information about Instagram profiles, businesses, or general topics',
    inputSchema: z.object({
      query: z.string().describe('The search query'),
      focus: z.enum(['instagram', 'business', 'general']).optional().describe('Focus area for the search')
    }),
    execute: async ({ query, focus }) => {
      try {
        console.log(`🔍 Real web search query: "${query}" (focus: ${focus || 'general'})`);

        // Get platform settings for Anthropic API
        const platformSettings = await prisma.platformSettings.findFirst();
        if (!platformSettings?.anthropicApiKey) {
          throw new Error('Anthropic API key not configured');
        }

        // Create Anthropic client
        const anthropic = createAnthropic({
          apiKey: platformSettings.anthropicApiKey,
        });

        // Build search query based on focus
        let searchQuery = query;
        if (focus === 'instagram') {
          searchQuery = `Instagram profile ${query} business information`;
        } else if (focus === 'business') {
          searchQuery = `${query} business company information`;
        }

        console.log(`🔍 Real web search query: "${searchQuery}" (focus: ${focus || 'general'})`);
        console.log(`🌐 Performing real web search: "${searchQuery}"`);

        // Make the web search API call
        const response = await anthropic.messages.create({
          model: 'claude-3-5-sonnet-20241022',
          max_tokens: 1000,
          tools: [
            {
              type: 'web_search_20250305',
              name: 'web_search',
              max_uses: 3
            }
          ],
          messages: [
            {
              role: 'user',
              content: `Search for information about "${searchQuery}". 

Please provide a natural analysis of what you find, including:
- Business type and industry
- Target audience  
- Products or services offered
- Business goals and objectives
- Social media presence and engagement
- Any relevant contact information

Be conversational and helpful in your response.`
            }
          ]
        });

        console.log('✅ Web search API call completed successfully');

        // Extract the response content
        let searchResult = '';
        if (response.content && response.content.length > 0) {
          const textContent = response.content.find(block => block.type === 'text');
          if (textContent && 'text' in textContent) {
            searchResult = textContent.text;
          }
        }

        if (!searchResult) {
          searchResult = 'I was able to search for information, but didn\'t find specific details. Let me ask you directly about your business.';
        }

        console.log('✅ Returning natural conversation response from Claude');
        return searchResult;

      } catch (error) {
        console.error('❌ Web search error:', error);
        return `I encountered an issue while searching for information about "${query}". Let me ask you directly about your business instead.`;
      }
    },
  });
