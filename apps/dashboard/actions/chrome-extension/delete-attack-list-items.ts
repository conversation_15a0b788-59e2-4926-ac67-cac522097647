'use server';

import { revalidatePath } from 'next/cache';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { ValidationError } from '@workspace/common/errors';
import { prisma } from '@workspace/database/client';
import { z } from 'zod';

const deleteAttackListItemsSchema = z.object({
  itemIds: z.array(z.string().uuid()).min(1, 'At least one item must be selected')
});

export async function deleteAttackListItems(itemIds: string[]) {
  try {
    const ctx = await getAuthOrganizationContext();

    const result = deleteAttackListItemsSchema.safeParse({ itemIds });
    if (!result.success) {
      throw new ValidationError(JSON.stringify(result.error.flatten()));
    }

    // Delete the attack list items
    const deleteResult = await prisma.chromeExtensionAttackList.deleteMany({
      where: {
        id: {
          in: itemIds
        },
        organizationId: ctx.organization.id // Ensure user can only delete their own items
      }
    });

    // Revalidate the attack list page
    revalidatePath(`/organizations/${ctx.organization.slug}/chrome-extension/attack-list`);

    return {
      success: true,
      deletedCount: deleteResult.count
    };
  } catch (error) {
    console.error('Error deleting attack list items:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    };
  }
}