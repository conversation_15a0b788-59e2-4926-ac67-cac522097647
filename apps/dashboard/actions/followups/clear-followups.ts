'use server';

import { revalidatePath } from 'next/cache';

import { prisma } from '@workspace/database/client';
import { authOrganizationActionClient } from '~/actions/safe-action';

import { clearFollowupsSchema } from '~/schemas/followups/clear-followups-schema';

export const clearFollowups = authOrganizationActionClient
  .metadata({ actionName: 'clearFollowups' })
  .inputSchema(clearFollowupsSchema)
  .action(async ({ parsedInput, ctx }) => {
    // Parse followup IDs to get contact IDs and followup numbers
    const followupUpdates = new Map<string, Set<number>>();
    
    for (const followupId of parsedInput.followupIds) {
      const parts = followupId.split('-');
      if (parts.length < 2) {
        throw new Error(`Invalid followup ID format: ${followupId}`);
      }
      
      const followupNumber = parts[parts.length - 1];
      const contactId = parts.slice(0, -1).join('-');
      const followupNum = parseInt(followupNumber);
      
      if (isNaN(followupNum) || followupNum < 1 || followupNum > 6) {
        throw new Error(`Invalid followup number: ${followupNumber}`);
      }
      
      if (!followupUpdates.has(contactId)) {
        followupUpdates.set(contactId, new Set());
      }
      followupUpdates.get(contactId)!.add(followupNum);
    }

    // Update contacts to clear the specified followup fields
    await prisma.$transaction(async (tx) => {
      for (const [contactId, followupNumbers] of followupUpdates) {
        const updateData: any = {};
        
        // Build the update object to clear the followup fields
        for (const num of followupNumbers) {
          updateData[`followup${num}_text`] = null;
          updateData[`followup${num}_scheduledAt`] = null;
          updateData[`followup${num}_status`] = null;
          updateData[`followup${num}_sendViaExtension`] = false;
        }

        await tx.instagramContact.update({
          where: {
            id: contactId,
            organizationId: ctx.organization.id
          },
          data: updateData
        });
      }
    });

    revalidatePath(`/organizations/${ctx.organization.slug}/followups`);

    return { success: true, clearedCount: parsedInput.followupIds.length };
  });