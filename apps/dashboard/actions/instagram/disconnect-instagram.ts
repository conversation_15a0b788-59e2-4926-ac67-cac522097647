'use server';

import { revalidateTag } from 'next/cache';

import { prisma } from '@workspace/database/client';
import { authOrganizationActionClient } from '~/actions/safe-action';
import { Caching, OrganizationCacheKey } from '~/data/caching';

export const disconnectInstagram = authOrganizationActionClient
  .metadata({ actionName: 'disconnectInstagram' })
  .action(async ({ ctx }) => {
    // Update organization to disconnect Instagram
    await prisma.organization.update({
      where: { id: ctx.organization.id },
      data: {
        instagramToken: null,
        instagramAccountId: null,
        instagramWebhookId: null,
        instagramUsername: null,
        instagramName: null,
        instagramProfilePicture: null,
        instagramTokenExpiresAt: null,
        instagramIsConnected: false,
        instagramConnectedAt: null,
        instagramUpdatedAt: new Date()
      }
    });

    // Revalidate cache
    revalidateTag(
      Caching.createOrganizationTag(
        OrganizationCacheKey.InstagramSettings,
        ctx.organization.id
      )
    );
    
    return { success: true };
  });