'use server';

import { createHash } from 'crypto';
import { revalidateTag } from 'next/cache';

import { decodeBase64Image, resizeImage } from '@workspace/common/image';
import type { Maybe } from '@workspace/common/maybe';
import { type Prisma } from '@workspace/database';
import { prisma } from '@workspace/database/client';
import { getOrganizationLogoUrl } from '@workspace/routes';

import { authOrganizationActionClient } from '~/actions/safe-action';
import { Caching, OrganizationCacheKey, UserCacheKey } from '~/data/caching';
import { downloadImageAsBase64 } from '~/lib/instagram-image-utils';

type Transaction = Prisma.PrismaPromise<unknown>;

export const updateOrganizationLogoFromInstagram = authOrganizationActionClient
  .metadata({ actionName: 'updateOrganizationLogoFromInstagram' })
  .action(async ({ ctx }) => {
    try {
      console.log(`🔄 Updating organization logo from Instagram for ${ctx.organization.slug}`);

      // Get the Instagram profile picture URL from the organization
      const organization = await prisma.organization.findUnique({
        where: { id: ctx.organization.id },
        select: { instagramProfilePicture: true }
      });

      if (!organization?.instagramProfilePicture) {
        console.log('❌ No Instagram profile picture URL found');
        return { success: false, error: 'No Instagram profile picture URL found' };
      }

      // Download the Instagram profile picture
      const base64Image = await downloadImageAsBase64(organization.instagramProfilePicture);
      
      if (!base64Image) {
        console.log('❌ Failed to download Instagram profile picture');
        return { success: false, error: 'Failed to download Instagram profile picture' };
      }

      // Process the image using existing logic
      const { buffer, mimeType } = decodeBase64Image(base64Image);
      const data = await resizeImage(buffer, mimeType);
      const hash = createHash('sha256').update(data).digest('hex');
      const logoUrl = getOrganizationLogoUrl(ctx.organization.id, hash);

      const transactions: Transaction[] = [];

      // Delete existing organization logo
      transactions.push(
        prisma.organizationLogo.deleteMany({
          where: { organizationId: ctx.organization.id }
        })
      );

      // Create new organization logo
      transactions.push(
        prisma.organizationLogo.create({
          data: {
            organizationId: ctx.organization.id,
            data,
            contentType: mimeType,
            hash
          },
          select: {
            id: true // SELECT NONE
          }
        })
      );

      // Update organization with logo URL
      transactions.push(
        prisma.organization.update({
          where: { id: ctx.organization.id },
          data: { logo: logoUrl },
          select: {
            id: true // SELECT NONE
          }
        })
      );

      // Execute all transactions
      await prisma.$transaction(transactions);

      // Revalidate caches
      revalidateTag(
        Caching.createOrganizationTag(
          OrganizationCacheKey.OrganizationLogo,
          ctx.organization.id
        )
      );

      for (const membership of ctx.organization.memberships) {
        revalidateTag(
          Caching.createUserTag(UserCacheKey.Organizations, membership.userId)
        );
      }

      console.log(`✅ Successfully updated organization logo from Instagram profile picture`);
      return { success: true, logoUrl };

    } catch (error) {
      console.error('Error updating organization logo from Instagram:', error);
      return { success: false, error: 'Failed to update organization logo' };
    }
  });