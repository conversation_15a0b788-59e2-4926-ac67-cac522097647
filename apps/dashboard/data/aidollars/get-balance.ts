import 'server-only';

import { getAuthOrganizationContext } from '@workspace/auth/context';
import { BalanceService } from '@workspace/aidollars';

export interface AIDollarsBalanceData {
  balance: number;
  subscriptionTier: string;
  pricePerRequest: number;
  dailyUsageRate: number;
  nextTopupAmount: number;
}

export async function getAIDollarsBalance(): Promise<AIDollarsBalanceData> {
  const ctx = await getAuthOrganizationContext();
  const balanceService = new BalanceService();

  // Get or initialize balance
  let balanceInfo = await balanceService.getBalance(ctx.organization.id);

  if (!balanceInfo) {
    // Initialize balance for new organization
    balanceInfo = await balanceService.initializeBalance(ctx.organization.id);
  }

  // Get usage stats for daily usage rate
  const usageStats = await balanceService.getUsageStats(ctx.organization.id, 30);

  return {
    balance: balanceInfo.balance,
    subscriptionTier: 'Platinum', // Fixed tier name
    pricePerRequest: parseFloat(process.env.AI_REQUEST_COST || '0.10'),
    dailyUsageRate: usageStats.dailyAverage,
    nextTopupAmount: 1000, // Fixed top-up amount
  };
}