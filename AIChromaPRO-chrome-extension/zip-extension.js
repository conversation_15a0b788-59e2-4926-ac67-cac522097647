#!/usr/bin/env node

import fs from 'fs';
import path from 'path';
import archiver from 'archiver';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const distPath = path.join(__dirname, 'dist');
const outputPath = path.join(__dirname, '../apps/dashboard/public/downloads');
const zipFileName = 'ai-insta-chrome-extension.zip';
const zipFilePath = path.join(outputPath, zipFileName);

// Ensure output directory exists
if (!fs.existsSync(outputPath)) {
  fs.mkdirSync(outputPath, { recursive: true });
}

// Remove existing zip file if it exists
if (fs.existsSync(zipFilePath)) {
  fs.unlinkSync(zipFilePath);
}

// Create a file to stream archive data to
const output = fs.createWriteStream(zipFilePath);
const archive = archiver('zip', {
  zlib: { level: 9 } // Maximum compression
});

// Listen for all archive data to be written
output.on('close', function() {
  const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
  console.log(`✅ Chrome extension zipped successfully!`);
  console.log(`📦 Size: ${sizeInMB} MB`);
  console.log(`📁 Location: /downloads/${zipFileName}`);
  console.log(`🔗 Available at: /downloads/${zipFileName}`);
});

// Handle warnings
archive.on('warning', function(err) {
  if (err.code === 'ENOENT') {
    console.warn('Warning:', err);
  } else {
    throw err;
  }
});

// Handle errors
archive.on('error', function(err) {
  console.error('❌ Error creating zip:', err);
  throw err;
});

// Pipe archive data to the file
archive.pipe(output);

// Add the dist directory contents to the zip
archive.directory(distPath, false);

// Finalize the archive
archive.finalize();