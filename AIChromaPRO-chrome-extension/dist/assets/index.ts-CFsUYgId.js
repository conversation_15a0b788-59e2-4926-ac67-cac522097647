var y=Object.defineProperty;var I=(p,e,t)=>e in p?y(p,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):p[e]=t;var u=(p,e,t)=>I(p,typeof e!="symbol"?e+"":e,t);import{b as i}from"./browser-polyfill-DQnhcI3D.js";class g{static async wait(e){return new Promise(t=>setTimeout(t,e))}static getRandomInterval(e,t){return Math.floor(Math.random()*(t-e+1))+e}static async waitBetweenUsers(e){const t=typeof e.delayBetweenDmsMin=="number"&&!isNaN(e.delayBetweenDmsMin)?e.delayBetweenDmsMin:5,s=typeof e.delayBetweenDmsMax=="number"&&!isNaN(e.delayBetweenDmsMax)?e.delayBetweenDmsMax:15,a=this.getRandomInterval(t*6e4,s*6e4);return console.log(`⏳ Starting ${Math.round(a/6e4)} minute wait before next user (${t}-${s}min range)`),await this.waitWithCountdown(a,"before next user"),console.log(`✅ Finished waiting ${Math.round(a/6e4)} minutes, ready for next user`),a}static async waitBetweenMessageLines(){const e=5e3+Math.random()*3e4;return console.log(`⏳ Starting ${Math.round(e/1e3)}s wait before next message line`),await this.waitWithCountdown(e,"before next message line"),console.log(`✅ Finished waiting ${Math.round(e/1e3)}s, sending next message line`),e}static async waitWithCountdown(e,t){const a=Date.now()+e;if(e>12e4){const c=setInterval(()=>{const r=Date.now(),l=a-r;if(l<=0){clearInterval(c);return}const n=Math.floor(l/6e4),h=Math.floor(l%6e4/1e3);n>0?console.log(`⏳ Still waiting ${n}m ${h}s ${t}...`):console.log(`⏳ Still waiting ${h}s ${t}...`)},3e4);setTimeout(()=>clearInterval(c),e)}await this.wait(e)}}class k{constructor(){u(this,"isProcessing",!1)}canProcess(){return!this.isProcessing}startProcessing(){return this.isProcessing?!1:(this.isProcessing=!0,!0)}stopProcessing(){this.isProcessing=!1}isCurrentlyProcessing(){return this.isProcessing}}class M{constructor(){u(this,"settings",null)}updateSettings(e){this.settings=e}getSettings(){return this.settings}getSafeSettings(){return this.settings?this.settings:(console.warn("⚠️ Settings not loaded, using defaults"),{apiKey:"",baseUrl:"http://localhost:3000",isActive:!1,maxActionsPerDay:50,dmBeforeBreakMin:7,dmBeforeBreakMax:10,breakTimeMin:10,breakTimeMax:30,delayBetweenDmsMin:5,delayBetweenDmsMax:15,naturalStopStart:"22:00",naturalStopEnd:"08:00"})}async getAttackQueue(){if(!this.settings)return[];try{const e=await fetch(`${this.settings.baseUrl}/api/chrome-extension/attack-queue?apiKey=${this.settings.apiKey}&limit=1`);if(!e.ok)throw new Error(`API error: ${e.status}`);return(await e.json()).messages||[]}catch(e){return console.error("Error fetching attack queue:",e),[]}}async markMessageSent(e){if(this.settings)try{await fetch(`${this.settings.baseUrl}/api/chrome-extension/attack-queue`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({apiKey:this.settings.apiKey,messageId:e,action:"mark_sent"})})}catch(t){console.error("Error marking message as sent:",t)}}async markMessageFailed(e,t){if(this.settings)try{await fetch(`${this.settings.baseUrl}/api/chrome-extension/attack-queue`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({apiKey:this.settings.apiKey,messageId:e,action:"mark_failed"})})}catch(s){console.error("Error marking message as failed:",s)}}async markMessageSkipped(e){if(this.settings)try{await fetch(`${this.settings.baseUrl}/api/chrome-extension/attack-queue`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({apiKey:this.settings.apiKey,messageId:e,action:"mark_skipped"})})}catch(t){console.error("Error marking message as skipped:",t)}}async uploadFollowers(e,t=!1){if(this.settings)try{const s=await fetch(`${this.settings.baseUrl}/api/chrome-extension/followers`,{method:"POST",headers:{Authorization:`Bearer ${this.settings.apiKey}`,"Content-Type":"application/json"},body:JSON.stringify({apiKey:this.settings.apiKey,followers:e,isInitialScrapingComplete:t})});if(!s.ok)throw new Error(`Upload failed: ${s.status}`)}catch(s){console.error("Error uploading followers:",s)}}async getRecentFollowerUsernames(){if(!this.settings)return[];try{const e=await fetch(`${this.settings.baseUrl}/api/chrome-extension/followers?apiKey=${this.settings.apiKey}&usernamesOnly=true`);if(!e.ok)throw new Error(`API error: ${e.status}`);return(await e.json()).followerUsernames||[]}catch(e){return console.error("Error fetching recent follower usernames:",e),[]}}async getScrapingStatus(){if(!this.settings)throw console.error("❌ No settings available for API call"),new Error("Extension settings not configured");if(!this.settings.apiKey)throw console.error("❌ No API key in settings"),new Error("API key not configured");try{const e=`${this.settings.baseUrl}/api/chrome-extension/settings?apiKey=${this.settings.apiKey}`;console.log("🌐 Fetching from:",e);const t=await fetch(e,{method:"GET",headers:{"Content-Type":"application/json"}});if(console.log("📡 API Response status:",t.status),!t.ok){const a=await t.text();throw console.error("❌ API error response:",a),new Error(`API error ${t.status}: ${a||"Unknown error"}`)}const s=await t.json();return console.log("✅ API data received:",s),s}catch(e){throw console.error("❌ Error in getScrapingStatus:",e),e}}async getUserProfile(){if(!this.settings)return null;try{const e=await fetch(`${this.settings.baseUrl}/api/chrome-extension/settings?apiKey=${this.settings.apiKey}`);if(!e.ok)throw new Error(`API error: ${e.status}`);return await e.json()}catch(e){return console.error("Error getting user profile:",e),null}}async updateScrapingStatus(e){if(!this.settings)throw new Error("Extension settings not configured");if(!this.settings.apiKey)throw new Error("API key not configured");try{console.log(`🔄 Updating scraping status to: ${e}`);const t=await fetch(`${this.settings.baseUrl}/api/chrome-extension/scraping-status`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({apiKey:this.settings.apiKey,scrapingStatus:e})});if(!t.ok){const s=await t.text();throw new Error(`Failed to update scraping status: ${t.status} - ${s}`)}console.log(`✅ Successfully updated scraping status to: ${e}`)}catch(t){throw console.error("❌ Error updating scraping status:",t),t}}}class C{constructor(){u(this,"scheduledActions",new Map)}scheduleAction(e,t,s){this.cancelAction(e);const a=setTimeout(()=>{t(),this.scheduledActions.delete(e)},s);this.scheduledActions.set(e,a)}cancelAction(e){const t=this.scheduledActions.get(e);t&&(clearTimeout(t),this.scheduledActions.delete(e))}cancelAllActions(){for(const e of this.scheduledActions.values())clearTimeout(e);this.scheduledActions.clear()}}class ${constructor(){u(this,"queue",[]);u(this,"isProcessing",!1);u(this,"isRunning",!1)}add(e){this.queue.push(e),console.log(`📋 Added task: ${e.name} (Queue: ${this.queue.length})`)}start(){this.isRunning||(this.isRunning=!0,console.log("🔄 TaskQueue started"),this.processQueue())}stop(){this.isRunning=!1,console.log("🛑 TaskQueue stopped")}clear(){this.queue=[],console.log("🗑️ TaskQueue cleared")}async processQueue(){for(;this.isRunning;){if(this.queue.length===0){await new Promise(t=>setTimeout(t,1e3));continue}if(this.isProcessing){await new Promise(t=>setTimeout(t,100));continue}const e=this.queue.shift();if(e){this.isProcessing=!0,console.log(`▶️ Executing task: ${e.name}`);try{await e.execute(),console.log(`✅ Completed task: ${e.name}`)}catch(t){console.error(`❌ Task failed: ${e.name}`,t)}this.isProcessing=!1}}}getQueueSize(){return this.queue.length}isCurrentlyProcessing(){return this.isProcessing}}class b{constructor(){u(this,"apiClient");u(this,"scheduler");u(this,"taskQueue");u(this,"stateManager");u(this,"mainLoopInterval",null);u(this,"monitoringInterval",null);u(this,"messageQueueInterval",null);u(this,"followerCheckInterval",null);u(this,"followerUploadInterval",null);u(this,"midnightResetInterval",null);u(this,"mainLoopCountdownInterval",null);u(this,"settingsRefreshInterval",null);u(this,"state",{isRunning:!1,todayActions:0,consecutiveDMs:0,pendingMessages:0,scrapingStatus:"UNKNOWN",scrapingProgress:{current:0,target:5e3,isInitialScraping:!1}});this.apiClient=new M,this.scheduler=new C,this.taskQueue=new $,this.stateManager=new k,this.init()}async init(){await this.loadState(),await this.checkAndResetDailyActions(),await this.loadApiSettings(),this.setupPeriodicTasks(),i.runtime.onMessage.addListener(this.handleMessage.bind(this))}async loadApiSettings(){try{console.log("🔄 Loading settings from API...");const e=this.apiClient.getSafeSettings();this.apiClient.updateSettings(e);const t=await this.apiClient.getScrapingStatus();if(t){const s={...e,...t,apiKey:e.apiKey,baseUrl:e.baseUrl};this.apiClient.updateSettings(s),console.log("✅ API settings loaded:",s)}else console.log("⚠️ No API settings received, using defaults")}catch(e){console.error("❌ Failed to load API settings, using defaults:",e)}}async refreshSettingsFromAPI(){try{console.log("🔄 Refreshing settings from API...");const e=this.apiClient.getSettings();if(!(e!=null&&e.apiKey)){console.log("⚠️ No API key available, skipping settings refresh");return}const t=await this.apiClient.getScrapingStatus();if(t){const s={...e,...t,apiKey:e.apiKey,baseUrl:e.baseUrl};this.hasSettingsChanged(e,s)?(console.log("✅ Settings updated from API:",{maxActionsPerDay:s.maxActionsPerDay,dmBeforeBreakMin:s.dmBeforeBreakMin,dmBeforeBreakMax:s.dmBeforeBreakMax,breakTimeMin:s.breakTimeMin,breakTimeMax:s.breakTimeMax,delayBetweenDmsMin:s.delayBetweenDmsMin,delayBetweenDmsMax:s.delayBetweenDmsMax,naturalStopStart:s.naturalStopStart,naturalStopEnd:s.naturalStopEnd}),this.apiClient.updateSettings(s),await i.storage.local.set({settings:s})):console.log("📊 Settings unchanged, no update needed")}else console.log("⚠️ No API settings received during refresh")}catch(e){console.error("❌ Failed to refresh settings from API:",e)}}hasSettingsChanged(e,t){return["maxActionsPerDay","dmBeforeBreakMin","dmBeforeBreakMax","breakTimeMin","breakTimeMax","delayBetweenDmsMin","delayBetweenDmsMax","naturalStopStart","naturalStopEnd"].some(a=>e[a]!==t[a])}async loadState(){const e=await i.storage.local.get(["extensionState","settings"]);e.extensionState&&(this.state={...this.state,...e.extensionState}),e.settings&&this.apiClient.updateSettings(e.settings)}async saveState(){await i.storage.local.set({extensionState:this.state})}async checkAndResetDailyActions(){const e=new Date().toDateString(),t=this.state.lastActionDate;console.log(`📅 Checking daily reset - Today: ${e}, Last action date: ${t||"never"}`),!t||t!==e?(console.log("🔄 New day detected! Resetting daily counters..."),console.log(`📊 Previous day stats: ${this.state.todayActions} actions completed`),this.state.todayActions=0,this.state.consecutiveDMs=0,this.state.currentBreakEnd=void 0,this.state.lastActionDate=e,await this.saveState(),console.log(`✅ Daily counters reset for ${e}`)):console.log(`📊 Same day - keeping existing counters: ${this.state.todayActions} actions today`)}setupPeriodicTasks(){this.clearPeriodicTasks(),this.messageQueueInterval=setInterval(()=>{this.state.isRunning&&this.state.scrapingStatus!=="INITIAL_SCRAPING"&&this.shouldTakeAction()&&this.processMessageQueue()},g.getRandomInterval(12e4,3e5)),this.followerCheckInterval=setInterval(()=>{if(console.log(`🔍 Follower check interval fired - isRunning: ${this.state.isRunning}, scrapingStatus: ${this.state.scrapingStatus}`),this.state.isRunning&&this.state.scrapingStatus==="SCRAPING_COMPLETED"){const e=new Date().toLocaleTimeString();console.log(`⏰ [${e}] BACKGROUND: Checking for new followers...`),this.checkForNewFollowers()}else this.state.isRunning&&this.state.scrapingStatus==="INITIAL_SCRAPING"?console.log("⏸️ Follower monitoring blocked - still in INITIAL_SCRAPING phase"):console.log("⏸️ Follower check skipped - extension not running")},3e4),this.followerUploadInterval=setInterval(()=>{this.state.isRunning&&this.processCollectedFollowers()},g.getRandomInterval(6e5,9e5)),this.settingsRefreshInterval=setInterval(()=>{this.state.isRunning&&this.refreshSettingsFromAPI()},g.getRandomInterval(3e5,6e5)),this.midnightResetInterval=setInterval(()=>{const e=new Date;e.getHours()===0&&e.getMinutes()===0&&(this.state.todayActions=0,this.state.consecutiveDMs=0,this.state.currentBreakEnd=void 0,this.state.lastActionDate=e.toDateString(),this.saveState(),console.log(`🌙 Midnight reset completed for ${e.toDateString()}`))},6e4),console.log("✅ All periodic tasks set up successfully")}clearPeriodicTasks(){const e=[{name:"messageQueueInterval",ref:this.messageQueueInterval},{name:"followerCheckInterval",ref:this.followerCheckInterval},{name:"followerUploadInterval",ref:this.followerUploadInterval},{name:"midnightResetInterval",ref:this.midnightResetInterval},{name:"mainLoopCountdownInterval",ref:this.mainLoopCountdownInterval},{name:"settingsRefreshInterval",ref:this.settingsRefreshInterval}];for(const t of e)t.ref&&(console.log(`🔄 Clearing ${t.name}...`),clearInterval(t.ref));this.messageQueueInterval=null,this.followerCheckInterval=null,this.followerUploadInterval=null,this.midnightResetInterval=null,this.mainLoopCountdownInterval=null,this.settingsRefreshInterval=null,console.log("✅ All periodic tasks cleared")}shouldTakeAction(){const e=this.apiClient.getSafeSettings();if(console.log("🔍 shouldTakeAction debug - Current stats:",{todayActions:this.state.todayActions,maxActionsPerDay:e.maxActionsPerDay,consecutiveDMs:this.state.consecutiveDMs,currentBreakEnd:this.state.currentBreakEnd?new Date(this.state.currentBreakEnd).toLocaleTimeString():"none",naturalStopStart:e.naturalStopStart,naturalStopEnd:e.naturalStopEnd}),this.state.todayActions>=e.maxActionsPerDay)return console.log(`❌ Daily limit reached: ${this.state.todayActions}/${e.maxActionsPerDay}`),!1;if(this.state.currentBreakEnd&&Date.now()<this.state.currentBreakEnd){const a=Math.round((this.state.currentBreakEnd-Date.now())/6e4);return a>0&&console.log(`😴 Still on break for ${a} more minutes (ends at ${new Date(this.state.currentBreakEnd).toLocaleTimeString()})`),!1}this.state.currentBreakEnd&&Date.now()>=this.state.currentBreakEnd&&(console.log("✅ Break ended, resuming automation"),this.state.currentBreakEnd=void 0,this.saveState());const t=new Date,s=`${t.getHours().toString().padStart(2,"0")}:${t.getMinutes().toString().padStart(2,"0")}`;if(console.log(`🕐 Time check: Current=${s}, StopWindow=${e.naturalStopStart}-${e.naturalStopEnd}`),e.naturalStopStart<=e.naturalStopEnd){if(s>=e.naturalStopStart&&s<=e.naturalStopEnd)return console.log(`❌ In natural stop window (same day): ${s} is between ${e.naturalStopStart}-${e.naturalStopEnd}`),!1;console.log(`✅ Outside stop window (same day): ${s} is NOT between ${e.naturalStopStart}-${e.naturalStopEnd}`)}else{if(s>=e.naturalStopStart||s<=e.naturalStopEnd){const a=s>=e.naturalStopStart,o=s<=e.naturalStopEnd;return console.log(`❌ In natural stop window (overnight): ${s} is in ${e.naturalStopStart}-${e.naturalStopEnd} (late evening: ${a}, early morning: ${o})`),!1}console.log(`✅ Outside stop window (overnight): ${s} is NOT in ${e.naturalStopStart}-${e.naturalStopEnd}`)}return console.log("✅ All checks passed - should take action"),!0}getAPIClient(){return this.apiClient}getState(){return this.state}startMainLoop(){console.log("🔄 Starting main loop for message processing..."),this.mainLoopInterval&&(console.log("🔄 Clearing existing main loop interval before starting new one..."),clearInterval(this.mainLoopInterval),this.mainLoopInterval=null),this.stateManager.isCurrentlyProcessing()&&(console.log("🔓 Clearing processing lock before starting main loop..."),this.stateManager.stopProcessing()),this.processMessages(),this.mainLoopInterval=setInterval(()=>{this.state.isRunning&&(console.log("🔄 Main loop cycle - checking for messages..."),this.processMessages())},6e4),this.startMainLoopCountdown(),console.log("✅ Main loop started - checking for messages every minute")}startMainLoopCountdown(){const e=setInterval(()=>{if(!this.state.isRunning){clearInterval(e);return}const t=new Date,s=new Date(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes()+1,0,0),a=Math.floor((s.getTime()-t.getTime())/1e3);a<=45&&a>0&&console.log(`📊 Next main loop check in ${a} seconds...`)},15e3);this.mainLoopCountdownInterval||(this.mainLoopCountdownInterval=e)}async processMessages(){if(this.state.isRunning){if(this.state.scrapingStatus==="INITIAL_SCRAPING"){console.log("⏸️ Blocking message automation - still in INITIAL_SCRAPING phase");return}if(!this.shouldTakeAction()){console.log("⏸️ Not taking action due to limits (daily limit, break time, or natural stop hours)");return}if(!this.stateManager.startProcessing()){console.log("⏸️ Message processing already in progress, skipping...");return}try{if(this.state.currentMessageProgress){await this.continueMessageProgress();return}console.log("🔍 Checking for messages to send...");const e=await this.apiClient.getAttackQueue();if(e.length===0){console.log("📭 No messages to send - but ensuring Instagram tab is available for monitoring"),(await i.tabs.query({url:"https://www.instagram.com/*"})).length===0?(console.log("📱 Opening Instagram tab for monitoring..."),await i.tabs.create({url:"https://www.instagram.com/",active:!1}),console.log("✅ Instagram tab opened and ready for monitoring")):console.log("✅ Instagram tab already available");return}console.log(`📤 Found ${e.length} messages to send`),e.length>1&&console.log(`🔄 Message queue status: Processing 1/${e.length} users`);const t=e[0];await this.startMessageProcessing(t)}catch(e){console.error("❌ Error in processMessages:",e)}finally{this.stateManager.stopProcessing()}}}async startMessageProcessing(e){try{let t=await i.tabs.query({url:"https://www.instagram.com/*"}),s;t.length>0?(s=t[0],await i.tabs.update(s.id,{active:!1,url:"https://www.instagram.com/"}),await this.waitForTabLoad(s.id),await g.wait(3e3)):(console.log("📱 Creating new Instagram tab for message processing..."),s=await i.tabs.create({url:"https://www.instagram.com/",active:!1}),await this.waitForTabLoad(s.id),await g.wait(5e3));try{await this.waitForContentScript(s.id),console.log("✅ Content script ready, processing messages...")}catch{console.log("🔄 Content script failed, refreshing Instagram tab..."),await i.tabs.reload(s.id),await g.wait(5e3);try{await this.waitForContentScript(s.id),console.log("✅ Content script ready after refresh")}catch(l){throw new Error(`Content script connection failed: ${l instanceof Error?l.message:"Unknown error"}`)}}console.log(`📤 Starting message processing for @${e.nickname}`),console.log(`📍 Navigating to profile: @${e.nickname}`);const a=`https://www.instagram.com/${e.nickname}/`;if(await i.tabs.update(s.id,{url:a,active:!1}),await this.waitForTabLoad(s.id),await g.wait(4e3),console.log(`📱 Clicking message button for @${e.nickname}`),await this.sendMessageWithRetry(s.id,{action:"VISIT_USER_MESSAGES",username:e.nickname},`Visit Messages for ${e.nickname}`),await g.wait(6e3),e.priority===1||e.messageSource==="chrome_extension_batch"){console.log(`🔍 NEW FOLLOWER: Checking if @${e.nickname} has existing conversation...`);try{const r=await this.sendMessageWithRetry(s.id,{action:"CHECK_ZERO_MESSAGES",username:e.nickname},`Check Zero Messages for ${e.nickname}`);if(r&&r.result==="no"){console.log(`⚠️ [${new Date().toLocaleTimeString()}] @${e.nickname} has EXISTING conversation - marking as SKIPPED`),await this.apiClient.markMessageSkipped(e.id),console.log(`✅ Successfully marked @${e.nickname} as SKIPPED`);return}else console.log(`✅ [${new Date().toLocaleTimeString()}] @${e.nickname} has ZERO messages - proceeding with message`)}catch(r){console.error(`❌ Error checking zero messages for @${e.nickname}:`,r)}}else console.log(`📩 FOLLOWUP: Skipping zero-message check for @${e.nickname} (priority: ${e.priority}, source: ${e.messageSource}) - proceeding with followup message`);const c=e.messageContent||[];this.state.currentMessageProgress={messageId:e.id,username:e.nickname||"unknown",currentLineIndex:0,totalLines:c.length,isInDMPage:!0},await this.saveState(),await this.sendCurrentMessageLine()}catch(t){console.error(`❌ Error starting message processing for @${e.nickname}:`,t),await this.apiClient.markMessageFailed(e.id,t instanceof Error?t.message:"Unknown error"),this.clearMessageProgress()}}async continueMessageProgress(){const e=this.state.currentMessageProgress;if(e.nextLineTime&&Date.now()<e.nextLineTime){console.log(`⏳ Still waiting to send message line ${e.currentLineIndex+1}/${e.totalLines} to @${e.username}`);return}console.log(`📤 Continuing message progress for @${e.username} (line ${e.currentLineIndex+1}/${e.totalLines})`);try{await this.sendCurrentMessageLine()}catch(t){console.error("❌ Error continuing message progress:",t),await this.apiClient.markMessageFailed(e.messageId,t instanceof Error?t.message:"Unknown error"),this.clearMessageProgress()}}async sendCurrentMessageLine(){const e=this.state.currentMessageProgress,s=(await this.apiClient.getAttackQueue()).find(l=>l.id===e.messageId);if(!s){console.log(`📭 Message ${e.messageId} no longer in queue, completing processing`),this.clearMessageProgress();return}const o=(s.messageContent||[])[e.currentLineIndex];if(!o){console.log(`✅ All messages sent to @${e.username}, marking as complete`),await this.apiClient.markMessageSent(e.messageId),this.state.todayActions++,this.state.lastActionDate=new Date().toDateString(),this.clearMessageProgress();return}console.log(`📤 Sending message line ${e.currentLineIndex+1}/${e.totalLines} to @${e.username}: "${o}"`),e.lastSentMessage=o,e.lastError=null,await this.saveState();const c=await i.tabs.query({url:"https://www.instagram.com/*"});if(c.length===0)throw new Error("Instagram tab not found");const r=c[0];try{const l=await this.sendMessageWithRetry(r.id,{action:"SEND_MESSAGE",username:e.username,message:o,type:"normal"},`Send Message ${e.currentLineIndex+1}/${e.totalLines} to ${e.username}`);if(l&&l.success)if(console.log(`✅ Message line ${e.currentLineIndex+1}/${e.totalLines} sent successfully`),e.currentLineIndex++,e.currentLineIndex>=e.totalLines){console.log(`✅ All ${e.totalLines} messages sent successfully to @${e.username}`),await this.apiClient.markMessageSent(e.messageId),this.state.todayActions++,this.state.lastActionDate=new Date().toDateString(),this.state.consecutiveDMs=(this.state.consecutiveDMs||0)+1,this.clearMessageProgress();const n=this.apiClient.getSafeSettings(),h=typeof n.dmBeforeBreakMin=="number"&&!isNaN(n.dmBeforeBreakMin)?n.dmBeforeBreakMin:7,f=typeof n.dmBeforeBreakMax=="number"&&!isNaN(n.dmBeforeBreakMax)?n.dmBeforeBreakMax:10,d=g.getRandomInterval(h,f);if(this.state.consecutiveDMs>=d){console.log(`😴 Break time! Sent ${this.state.consecutiveDMs} consecutive DMs (trigger: ${d})`);const m=typeof n.breakTimeMin=="number"&&!isNaN(n.breakTimeMin)?n.breakTimeMin:10,w=typeof n.breakTimeMax=="number"&&!isNaN(n.breakTimeMax)?n.breakTimeMax:30,S=g.getRandomInterval(m*6e4,w*6e4);this.state.currentBreakEnd=Date.now()+S,this.state.consecutiveDMs=0,await this.saveState(),console.log(`😴 Starting ${Math.round(S/6e4)} minute break (${m}-${w}min range)`),console.log(`⏸️ Extension will pause until break ends at ${new Date(this.state.currentBreakEnd).toLocaleTimeString()}`)}else console.log(`📊 Consecutive DMs: ${this.state.consecutiveDMs}/${d} (break at ${d})`),console.log("⏳ Waiting between users before processing next message..."),await g.waitBetweenUsers(n)}else{const n=5e3+Math.random()*3e4;e.nextLineTime=Date.now()+n,console.log(`⏳ Scheduled next message line in ${Math.round(n/1e3)}s`),await this.saveState()}else throw new Error("Message send failed")}catch(l){const n=l instanceof Error?l.message:"Unknown error";console.error(`❌ Failed to send message line ${e.currentLineIndex+1}/${e.totalLines}:`,l),e.lastError=n,await this.saveState();const h=n.includes("timeout")||n.includes("timeout"),f=n.includes("connection")||n.includes("network")||n.includes("fetch"),d=n.includes("Receiving end does not exist")||n.includes("sendMessage");if(console.log("🔍 Error classification:",{isTimeoutError:h,isConnectionError:f,isCommunicationError:d,errorMessage:n}),h||d||n.includes("Message send failed"))if(console.log("🔍 Potential false negative detected - attempting enhanced recovery..."),e.lastError=n,await this.checkMessageSentAndContinue(e)){console.log("✅ Enhanced recovery SUCCESS - message was likely sent, continuing to next line"),e.currentLineIndex++,e.currentLineIndex>=e.totalLines?(console.log(`✅ All ${e.totalLines} messages completed for @${e.username} (recovered from ${h?"timeout":"communication"} error)`),await this.apiClient.markMessageSent(e.messageId),this.state.todayActions++,this.state.lastActionDate=new Date().toDateString(),this.clearMessageProgress()):(e.nextLineTime=Date.now()+8e3,console.log(`⏳ Scheduled next message line in 8s (recovered from ${h?"timeout":"communication"} error)`),await this.saveState());return}else console.log("❌ Enhanced recovery FAILED - message was likely not sent");else console.log("❌ Non-recoverable error type - marking as failed immediately");await this.apiClient.markMessageFailed(e.messageId,n),this.clearMessageProgress()}}async checkMessageSentAndContinue(e){try{console.log("🔍 Enhanced verification - checking if message was actually sent despite timeout..."),await g.wait(5e3);const t=await i.tabs.query({url:"https://www.instagram.com/*"});if(t.length===0)return console.log("❌ No Instagram tab found for verification"),!1;const s=t[0];try{console.log("🔍 Method 1: Checking conversation existence...");const a=await this.sendMessageWithRetry(s.id,{action:"CHECK_ZERO_MESSAGES",username:e.username},"Verify Message Sent",2);if(a&&a.result==="no")return console.log("✅ Method 1 SUCCESS: Verification suggests message was sent (conversation exists)"),!0;console.log("🤷 Method 1: Conversation state unclear, trying more verification methods...")}catch(a){console.log("⚠️ Method 1 failed, trying alternative methods:",a)}try{console.log("🔍 Method 2: Checking DOM state via content script...");const a=await this.sendMessageWithRetry(s.id,{action:"VERIFY_MESSAGE_IN_DOM",username:e.username,expectedMessage:e.lastSentMessage},"DOM Verification",1);if(a&&a.messageFound)return console.log("✅ Method 2 SUCCESS: Message found in DOM - was sent successfully"),!0;console.log("🤷 Method 2: Message not found in DOM")}catch(a){console.log("⚠️ Method 2 failed:",a)}try{console.log("🔍 Method 3: Heuristic analysis...");const a=e.lastError&&e.lastError.includes("timeout"),o=e.lastSentMessage&&e.lastSentMessage.length<500;if(a&&o)return console.log("✅ Method 3 SUCCESS: Timeout + simple message = likely sent successfully"),console.log("📊 Heuristic factors: timeout error + simple message suggests false negative"),!0;console.log("🤷 Method 3: Heuristics don't suggest message was sent")}catch(a){console.log("⚠️ Method 3 failed:",a)}return console.log("❌ All verification methods failed - message likely not sent"),!1}catch(t){return console.error("❌ Error in enhanced message verification:",t),!1}}clearMessageProgress(){this.state.currentMessageProgress=void 0,this.saveState(),console.log("🧹 Cleared message progress state")}async waitForTabLoad(e){return new Promise(t=>{const s=(a,o)=>{a===e&&o.status==="complete"&&(i.tabs.onUpdated.removeListener(s),t())};i.tabs.onUpdated.addListener(s),setTimeout(()=>{i.tabs.onUpdated.removeListener(s),t()},1e4)})}async waitForContentScript(e){for(let a=1;a<=5;a++)try{console.log(`🔄 Attempt ${a}/5 - Testing content script...`);const o=await i.tabs.sendMessage(e,{action:"PING"});if(o!=null&&o.success){console.log("✅ Content script is ready!");return}}catch(o){console.log(`❌ Attempt ${a} failed:`,o instanceof Error?o.message:"Unknown error"),a<5&&(console.log("⏳ Waiting 2000ms before next attempt..."),await g.wait(2e3))}throw new Error("Content script not ready after 5 attempts")}async sendMessageWithRetry(e,t,s,a=3){var r;let o=null;for(let l=1;l<=a;l++)try{return await i.tabs.get(e),await i.tabs.sendMessage(e,t)}catch(n){if(o=n,l===a)break;if((r=n.message)!=null&&r.includes("Receiving end does not exist")){const h=3e3+l*2e3;console.log(`Content script not ready, waiting ${h}ms before retry ${l}/${a}`),await g.wait(h)}else{const h=1e3*l;await g.wait(h)}}throw o||new Error(`${s} failed after ${a} attempts`)}async processMessageQueue(){return this.processMessages()}async processCollectedFollowers(){}async handleMessage(e,t){var s;switch(e.action){case"START_EXTENSION":return await this.startExtension();case"STOP_EXTENSION":return this.state.isRunning=!1,await this.stopMainLoop(),await this.saveState(),{success:!0};case"GET_STATE":return{state:this.state};case"UPDATE_SETTINGS":return await i.storage.local.set({settings:e.data}),this.apiClient.updateSettings(e.data),{success:!0};case"CHECK_SCRAPING_STATUS":return await this.checkScrapingStatus();case"START_INITIAL_SCRAPING":return await this.startInitialScraping();case"SCRAPING_PROGRESS_UPDATE":return this.updateScrapingProgress(e.data),{success:!0};case"INITIAL_SCRAPING_COMPLETE":return await this.completeInitialScraping();case"INITIAL_SCRAPING_FAILED":return console.error("❌ Initial scraping failed:",(s=e.data)==null?void 0:s.error),this.state.isRunning=!1,await this.saveState(),{success:!0};case"UPLOAD_FOLLOWER_BATCH":return await this.uploadFollowerBatch(e.data);case"MESSAGE_SENT":return await this.apiClient.markMessageSent(e.data.queueItemId),{success:!0};case"MESSAGE_FAILED":return await this.apiClient.markMessageFailed(e.data.queueItemId,e.data.error),{success:!0};default:return{error:"Unknown action"}}}async startExtension(){try{console.log("🧹 Performing comprehensive startup cleanup..."),this.stateManager.isCurrentlyProcessing()&&(console.log("🔓 Clearing existing processing lock..."),this.stateManager.stopProcessing()),this.mainLoopInterval&&(console.log("🔄 Clearing existing main loop interval..."),clearInterval(this.mainLoopInterval),this.mainLoopInterval=null),this.monitoringInterval&&(console.log("📊 Clearing existing monitoring interval..."),clearInterval(this.monitoringInterval),this.monitoringInterval=null),this.clearPeriodicTasks();try{const s=await i.tabs.query({url:"https://www.instagram.com/*"});console.log(`📱 Found ${s.length} existing Instagram tabs to close`);for(const a of s)a.id&&(await i.tabs.remove(a.id),console.log(`✅ Closed existing Instagram tab: ${a.id}`))}catch(s){console.error("⚠️ Error closing existing Instagram tabs:",s)}await g.wait(2e3),console.log("✅ Startup cleanup completed");const e=this.apiClient.getSettings();if(!e||!e.apiKey)return{success:!1,error:"Please configure API key first"};console.log("🔄 Starting extension with API key check...");const t=await this.checkScrapingStatus();if(!t.success)return console.error("❌ API connection failed:",t.error),{success:!1,error:`API connection failed: ${t.error}`};if(console.log("✅ API connection successful. Scraping status:",this.state.scrapingStatus),this.state.isRunning=!0,console.log("🔄 Restarting periodic tasks after cleanup..."),this.setupPeriodicTasks(),this.state.scrapingStatus==="SCRAPING_COMPLETED"){console.log("🔄 Doing catch-up scraping to find any missed followers...");const s=await this.doCatchUpScraping();s.success?s.newFollowersFound>0?console.log(`✅ Catch-up complete! Found ${s.newFollowersFound} new followers`):console.log("✅ Catch-up complete! No new followers found - already up to date"):console.log("⚠️ Catch-up scraping failed, continuing with automation anyway:",s.error),console.log("⏳ Waiting 5 seconds before starting main automation loop..."),await g.wait(5e3),console.log("🔄 Starting main automation loop for message processing..."),this.startMainLoop()}else{console.log("🔄 Initial scraping needed - starting scraping workflow...");const s=await this.startInitialScraping();if(s.success)console.log("✅ Initial scraping completed successfully"),console.log("🔄 Now starting main automation loop for message processing..."),this.startMainLoop();else return console.error("❌ Initial scraping failed:",s.error),this.state.isRunning=!1,await this.saveState(),{success:!1,error:`Initial scraping failed: ${s.error}`}}return await this.saveState(),{success:!0,scrapingStatus:this.state.scrapingStatus}}catch(e){return console.error("❌ Error starting extension:",e),this.state.isRunning=!1,await this.saveState(),{success:!1,error:e.message||"Unknown error occurred"}}}async checkScrapingStatus(){try{console.log("🔍 Checking scraping status from API...");const e=await this.apiClient.getScrapingStatus();return e?(console.log("✅ Received status from API:",e),this.state.scrapingStatus=e.scrapingStatus||"SCRAPING_COMPLETED",this.state.scrapingProgress={...this.state.scrapingProgress,isInitialScraping:this.state.scrapingStatus==="INITIAL_SCRAPING"},console.log("📊 Updated scraping progress:",this.state.scrapingProgress),console.log("📱 Instagram username:",e.instagramUsername),await this.saveState(),{success:!0,status:e}):(console.error("❌ API returned null/empty status"),{success:!1,error:"API returned empty response"})}catch(e){console.error("❌ Error checking scraping status:",e);let t="Unknown error";return e instanceof TypeError&&e.message.includes("Failed to fetch")?t="Network error - check if dashboard is running":e instanceof Error&&(t=e.message),{success:!1,error:t}}}async startInitialScraping(){try{if(!this.apiClient.getSettings())return{success:!1,error:"No settings found"};const t=await this.apiClient.getUserProfile();if(!(t!=null&&t.instagramUsername))return{success:!1,error:"Instagram username not configured in dashboard"};console.log("📱 Using Instagram username:",t.instagramUsername),console.log("📱 Opening new Instagram tab...");const a=(await i.tabs.create({url:"https://www.instagram.com",active:!1})).id;await g.wait(5e3),console.log("✅ Instagram tab loaded"),console.log("⏳ Waiting for content script to be ready..."),await this.waitForContentScript(a),console.log("📍 Step 1: Navigating to profile...");const o=`https://instagram.com/${t.instagramUsername}`;await i.tabs.update(a,{url:o,active:!1}),console.log("⏳ Waiting for profile page to load..."),await g.wait(5e3),console.log("⏳ Waiting for content script after navigation..."),await this.waitForContentScript(a),console.log("🚀 Step 2: Starting API-based follower scraping...");const c=await this.sendMessageWithRetry(a,{action:"SCRAPE_FOLLOWERS",count:5e3,username:t.instagramUsername},"Scrape Followers");if(console.log("📋 Scraping response:",c),!c||!c.success){const l=(c==null?void 0:c.error)||"Failed to scrape followers";throw console.error("❌ Scraping failed:",l),new Error(l)}const r=c.followers||[];return console.log(`📤 Uploading ${r.length} followers to API...`),r.length>0&&(await this.uploadFollowersInBatches(r),console.log("📝 Updating scraping status to SCRAPING_COMPLETED in database..."),await this.apiClient.updateScrapingStatus("SCRAPING_COMPLETED"),this.state.scrapingStatus="SCRAPING_COMPLETED",await this.saveState(),console.log("🎉 Initial scraping completed and uploaded to API!"),console.log("✅ Database updated: INITIAL_SCRAPING → SCRAPING_COMPLETED")),console.log("✅ Initial scraping workflow completed successfully"),{success:!0,followersCount:r.length}}catch(e){return console.error("❌ Error in initial scraping:",e),{success:!1,error:e instanceof Error?e.message:"Unknown error"}}}async uploadFollowersInBatches(e){const s=Math.ceil(e.length/50);console.log(`📤 Uploading ${e.length} followers in ${s} batches of 50...`);for(let a=0;a<e.length;a+=50){const o=e.slice(a,a+50),c=Math.floor(a/50)+1;try{console.log(`📤 Uploading batch ${c}/${s} (${o.length} followers)...`),await this.apiClient.uploadFollowers(o,!1),console.log(`✅ Batch ${c}/${s} uploaded successfully`),a+50<e.length&&await g.wait(1e3)}catch(r){throw console.error(`❌ Failed to upload batch ${c}:`,r),r}}console.log("🎉 All follower batches uploaded successfully!")}updateScrapingProgress(e){this.state.scrapingProgress&&(this.state.scrapingProgress.current=e.current,this.state.scrapingProgress.target=e.total,this.saveState())}async completeInitialScraping(){try{return this.state.scrapingStatus="SCRAPING_COMPLETED",this.state.scrapingProgress.isInitialScraping=!1,await this.saveState(),console.log("Initial scraping completed! Ready for normal automation."),{success:!0}}catch(e){return console.error("Error completing initial scraping:",e),{success:!1,error:e.message}}}async doCatchUpScraping(){if(!this.stateManager.startProcessing())return console.log("⚠️ Cannot start catch-up scraping - another process is running"),{success:!1,newFollowersFound:0,error:"Another process is running"};try{console.log("🔍 Starting catch-up scraping to find missed followers..."),console.log("📋 Fetching recent follower usernames from database...");const e=await this.apiClient.getRecentFollowerUsernames();console.log(`📊 Checking against ${e.length} recent follower usernames from database`),console.log("🔍 Sample database usernames:",e.slice(0,5));const t=await this.apiClient.getUserProfile();if(!(t!=null&&t.instagramUsername))throw new Error("Instagram username not configured in dashboard");console.log("📱 Using Instagram username:",t.instagramUsername);let a=(await i.tabs.query({url:"https://www.instagram.com/*"}))[0];a?(console.log("📱 Navigating to user profile for catch-up scraping..."),await i.tabs.update(a.id,{url:`https://www.instagram.com/${t.instagramUsername}/`,active:!1}),await this.waitForTabLoad(a.id),await g.wait(3e3)):(console.log("📱 Opening Instagram tab for catch-up scraping..."),a=await i.tabs.create({url:`https://www.instagram.com/${t.instagramUsername}/`,active:!1}),await this.waitForTabLoad(a.id),await g.wait(5e3)),await this.waitForContentScript(a.id),console.log("✅ Content script ready for catch-up scraping");let o=0,c=!1,r=1,l;for(console.log("🚀 Starting API-based catch-up scraping (no modal needed)...");!c;){console.log(`🔍 Catch-up scraping batch ${r} (pagination: ${l?"continuing":"from start"})...`);const n=await this.sendMessageWithRetry(a.id,{action:"SCRAPE_FOLLOWERS",count:100,batchNumber:r,username:t.instagramUsername,startMaxId:l},`Scrape Batch ${r}`,2);if(!n||!n.success){console.log(`⚠️ Scraping batch ${r} failed during catch-up, stopping catch-up (this is normal)`);break}const h=n.followers||[],f=n.nextMaxId;if(console.log(`📊 Batch ${r}: Found ${h.length} followers`),h.length===0){console.log("📭 No more followers to scrape, catch-up complete");break}const d=[];let m=0;console.log("🔍 Sample scraped followers:",h.slice(0,3).map(w=>({nickname:w.nickname,id:w.instagramUserId})));for(const w of h)e.includes(w.nickname)?(m++,console.log(`✅ Hit ${m}: Found existing follower @${w.nickname}`)):d.push(w);console.log(`📊 Batch ${r} results: ${m} hits, ${d.length} new followers`),d.length>0?(console.log(`📤 Uploading ${d.length} new followers from batch ${r}`),await this.apiClient.uploadFollowers(d,!1),o+=d.length):console.log(`📭 No new followers in batch ${r}`),m>=3?(console.log(`🎯 Found ${m} hits in batch ${r} (≥3), stopping catch-up scraping!`),c=!0):(console.log(`⏳ Only ${m} hits in batch ${r} (<3), continuing to next batch...`),l=f,await g.wait(3e3)),r++}return console.log(`✅ Catch-up scraping completed! Processed ${r-1} batches`),{success:!0,newFollowersFound:o}}catch(e){return console.error("❌ Error in catch-up scraping:",e),{success:!1,newFollowersFound:0,error:e instanceof Error?e.message:"Unknown error"}}finally{this.stateManager.stopProcessing(),console.log("🔓 Released processing lock from catch-up scraping")}}async uploadFollowerBatch(e){try{const{followers:t,isInitialScrapingComplete:s}=e;return console.log(`📤 Uploading ${t.length} followers to API. Complete: ${s}`),await this.apiClient.uploadFollowers(t,s),console.log("✅ Followers uploaded successfully"),{success:!0}}catch(t){return console.error("❌ Error uploading follower batch:",t),{success:!1,error:t.message}}}async checkForNewFollowers(){try{const e=new Date().toLocaleTimeString();console.log(`🔍 [${e}] BACKGROUND: Starting follower check...`);const t=await i.tabs.query({url:["*://www.instagram.com/*"]});if(!t.length){console.log(`❌ [${e}] BACKGROUND: No Instagram tab found for follower check`);return}const s=t[0];if(!s.id){console.log(`❌ [${e}] BACKGROUND: Invalid tab ID for follower check`);return}console.log(`📨 [${e}] BACKGROUND: Sending CHECK_NEW_FOLLOWERS to content script...`);const a=await i.tabs.sendMessage(s.id,{action:"CHECK_NEW_FOLLOWERS"});if(a&&a.success){const o=a.followers||[];console.log(`✅ [${e}] BACKGROUND: Found ${o.length} new followers`),o.length>0&&(console.log(`📤 [${e}] BACKGROUND: Uploading ${o.length} new followers...`),await this.apiClient.uploadFollowers(o))}else console.log(`⚠️ [${e}] BACKGROUND: Follower check failed:`,(a==null?void 0:a.error)||"Unknown error")}catch(e){const t=new Date().toLocaleTimeString();console.error(`❌ [${t}] BACKGROUND: Error in follower check:`,e)}}async stopMainLoop(){console.log("🛑 Stopping main loop and monitoring..."),this.state.isRunning=!1,this.mainLoopInterval&&(console.log("🔄 Clearing main loop interval..."),clearInterval(this.mainLoopInterval),this.mainLoopInterval=null),this.monitoringInterval&&(console.log("📊 Clearing monitoring interval..."),clearInterval(this.monitoringInterval),this.monitoringInterval=null),this.clearPeriodicTasks(),this.stateManager.isCurrentlyProcessing()&&(console.log("🔓 Clearing processing lock..."),this.stateManager.stopProcessing());try{const e=await i.tabs.query({url:"https://www.instagram.com/*"});console.log(`📱 Found ${e.length} Instagram tabs to close`);for(const t of e)t.id&&(await i.tabs.remove(t.id),console.log(`✅ Closed Instagram tab: ${t.id}`))}catch(e){console.error("❌ Error closing Instagram tabs:",e)}await this.saveState(),console.log("✅ Complete extension cleanup completed")}}console.log("🚀 AIChromaPRO Background Service - Modular Architecture");console.log("✅ Using KISS principle: Keep It As Simple As Possible");new b;
