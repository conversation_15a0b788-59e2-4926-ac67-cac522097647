var _=Object.defineProperty;var $=(w,e,t)=>e in w?_(w,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):w[e]=t;var p=(w,e,t)=>$(w,typeof e!="symbol"?e+"":e,t);import{b as A}from"./browser-polyfill-DQnhcI3D.js";class x{constructor(){p(this,"injectedScriptInitialized",!1)}async getHaveZeroMessages(e){try{if(console.log(`🔍 [${new Date().toLocaleTimeString()}] Checking if @${e} has zero messages...`),await this.delay(4e3),!window.location.href.includes("/direct"))return console.log("❌ Not in direct message conversation, can't check zero messages"),"unknown";const t=document.querySelectorAll('[data-scope="messages_table"]');console.log(`🔍 Messages found: ${t.length}`);let s;return t.length<=1?(s="yes",console.log(`✅ [${new Date().toLocaleTimeString()}] @${e} has ZERO messages - safe to send`)):(s="no",console.log(`⚠️ [${new Date().toLocaleTimeString()}] @${e} has EXISTING conversation (${t.length} messages) - will skip`)),s}catch(t){return console.error("❌ Error checking zero messages:",t),"unknown"}}async sendMessage(e){this.injectedScriptInitialized||(console.log("🔄 Injected script not initialized, initializing now..."),this.initializeInjectedScript(),await this.delay(2e3));try{const{username:t,message:s}=e;console.log(`📤 Sending message to @${t}: "${s}"`);const o=await this.resolveUsernameToUserId(t);if(!o)throw console.log("❌ Failed to resolve username to user ID:",t),new Error(`Failed to resolve username '${t}' to a user ID.`);console.log("✅ Resolved username to user ID:",{username:t,recipient_id:o});let n="unknown";const a=document.cookie.split(";");for(const i of a){const[g,l]=i.trim().split("=");if(g==="ds_user_id"&&l){n=l;break}}console.log("Viewer ID from cookies:",n);const c=window.location.pathname.match(/\/direct\/t\/(\d+)/);let d=c?c[1]:"unknown";if(console.log("Current URL analysis:",{pathname:window.location.pathname,urlMatch:c?c[0]:null,threadId:d}),d==="unknown"){console.log("No existing thread found, creating new one for recipient:",o);const i=await this.createGroupThread(o);if(i&&i.status==="ok")d=i.thread_id,console.log("✅ New thread created successfully:",d),await this.delay(1e3),console.log("✅ Thread creation completed, ready for messaging");else throw console.log("❌ Failed to create new thread:",i),new Error("Failed to create message thread")}else console.log("✅ Using existing thread:",d);const r=await new Promise((i,g)=>{let l=!1,u=Date.now(),h=!1;const y=15e3,S=45e3,E=f=>{if(console.log("📨 Content script received message event:",f.data),f.data&&f.data.type==="INJECT_HEARTBEAT"){u=Date.now(),h=!0,console.log(`💓 Heartbeat received: ${f.data.status||"processing"}`);return}f.data&&f.data.type==="INJECT_DISPATCH_DM_RESPONSE"&&(window.removeEventListener("message",E),l=!0,f.data.ret===1?(console.log("✅ Message sent successfully!"),i(!0)):(console.error("❌ Message failed:",f.data),g(new Error(`Message failed: ${f.data.error||f.data.status_code}`))))};window.addEventListener("message",E);const k={type:"INJECT_DISPATCH_DM_REQUEST",thread_id:d,viewer_id:n,user:{id:o,username:t},text:s,debug:!0};console.log("📤 Content script sending message request:",k),window.postMessage(k,"*");const b=Date.now(),I=()=>{if(l)return;const f=Date.now(),v=f-b,M=f-u;h&&v>S?(console.error(`⏰ Message timeout - no response after ${S/1e3}s (had heartbeat)`),window.removeEventListener("message",E),g(new Error("Message timeout - taking too long despite heartbeat"))):!h&&M>y?(console.error(`⏰ Message timeout - no heartbeat received within ${y/1e3}s`),window.removeEventListener("message",E),g(new Error("Message timeout - injected script not responding"))):setTimeout(I,2e3)};setTimeout(I,2e3)});console.log("Message sent successfully via injected script");try{await this.storeExtensionMessage(t,s,d,o)}catch(i){console.warn("Failed to store message in database:",i)}return{success:!0}}catch(t){return console.log("Error in sendMessage:",t),console.error("Error in sendMessage:",t),{success:!1,error:t.message}}}async resolveUsernameToUserId(e){var t,s;try{console.log("Attempting to resolve username to user ID:",e);const o=`https://www.instagram.com/api/v1/users/web_profile_info/?username=${e}`,n=await fetch(o,{credentials:"include",headers:{"User-Agent":navigator.userAgent,Accept:"application/json","x-ig-app-id":"936619743392459"}});if(n.ok){const a=await n.json(),c=(s=(t=a==null?void 0:a.data)==null?void 0:t.user)==null?void 0:s.id;if(c)return console.log(`Resolved ${e} to user ID: ${c}`),c}return console.log("Could not resolve username to user ID via web_profile_info API"),null}catch(o){return console.log("Error in resolveUsernameToUserId:",o),null}}async createGroupThread(e){let t="https://i.instagram.com/api/v1/direct_v2/create_group_thread/",s=await this.getCsrfToken();const o={recipient_users:'["'.concat(e,'"]')},n=new URLSearchParams([...Object.entries(o)]).toString();return await(await fetch(t,{credentials:"include",headers:{accept:"application/json, text/plain, */*","content-type":"application/x-www-form-urlencoded",Referer:"https://www.instagram.com/","Referrer-Policy":"strict-origin-when-cross-origin","x-asbd-id":"129477","X-IG-App-ID":"936619743392459","x-instagram-ajax":"1","X-CSRFToken":s,"x-requested-with":"XMLHttpRequest"},body:n,method:"POST"})).json()}async getCsrfToken(){try{const e=document.querySelector('meta[name="csrf-token"]');if(e){const o=e.getAttribute("content");if(o)return console.log("✅ Got CSRF token from meta tag"),o}const t=document.cookie.split(";");for(const o of t){const[n,a]=o.trim().split("=");if(n==="csrftoken")return console.log("✅ Got CSRF token from cookie"),a}const s=document.querySelectorAll("script:not([src])");for(const o of s){const a=(o.textContent||"").match(/"csrf_token":"([^"]+)"/);if(a)return console.log("✅ Got CSRF token from script"),a[1]}return console.log("❌ Could not find CSRF token"),null}catch(e){return console.error("❌ Error getting CSRF token:",e),null}}delay(e){return new Promise(t=>setTimeout(t,e))}async verifyMessageInDOM(e,t){try{if(console.log(`🔍 [${new Date().toLocaleTimeString()}] Verifying if message "${t}" appears in DOM for @${e}`),await this.delay(2e3),!window.location.href.includes("/direct"))return console.log("❌ Not in direct message conversation, cannot verify message in DOM"),!1;const s=['[data-scope="messages_table"] [dir="auto"]','[role="log"] [data-testid*="message"]','.x1n2onr6 [role="presentation"] [dir="auto"]','[data-scope="messages_table"] span','[role="log"] span'];for(const o of s)try{const n=document.querySelectorAll(o);for(const a of n)if((a.textContent||a.innerText||"").trim()===t.trim())return console.log(`✅ Found expected message "${t}" in DOM using selector: ${o}`),!0}catch(n){console.log(`⚠️ Selector failed (non-critical): ${o}`,n)}if(t.length>10){const o=t.split(" ").filter(n=>n.length>3).slice(0,3);for(const n of s)try{const a=document.querySelectorAll(n);for(const c of a){const d=c.textContent||c.innerText||"",m=o.filter(r=>d.toLowerCase().includes(r.toLowerCase()));if(m.length>=2)return console.log(`✅ Found message with ${m.length}/${o.length} keywords: ${m.join(", ")}`),!0}}catch(a){console.log(`⚠️ Keyword check failed (non-critical): ${n}`,a)}}return console.log(`❌ Expected message "${t}" not found in DOM`),!1}catch(s){return console.error("❌ Error verifying message in DOM:",s),!1}}initializeInjectedScript(){console.log("🔄 Initializing injected script for Instagram DM handling..."),console.log("Script URL:",chrome.runtime.getURL("src/injected/instagram-dm-handler.js"));const e=document.createElement("script");e.src=chrome.runtime.getURL("src/injected/instagram-dm-handler.js"),e.onload=()=>{console.log("✅ Injected DM handler script loaded successfully"),console.log("Checking if instagramLocalStorageHandler is available:",!!window.instagramLocalStorageHandler),this.injectedScriptInitialized=!0,e.remove()},e.onerror=t=>{console.error("❌ Failed to load injected DM handler script:",t),console.error("Error details:",t),this.injectedScriptInitialized=!1,e.remove()},console.log("📝 Appending script to document head..."),document.head.appendChild(e)}async storeExtensionMessage(e,t,s,o){try{const a=(await A.storage.sync.get(["organizationId"])).organizationId;if(!a)throw new Error("Organization ID not found in extension storage");const d=await fetch("https://app.setorai.com/api/chrome-extension/messages",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({organizationId:a,recipientInstagramUserId:o,recipientInstagramUsername:e,messageText:t,threadId:s,timestamp:Date.now()})});if(!d.ok)throw new Error(`API call failed: ${d.status} ${d.statusText}`);const m=await d.json();console.log("✅ Extension message stored in database:",m)}catch(n){throw console.error("❌ Failed to store extension message:",n),n}}}class D{async visitUserMessages(e){var t,s;try{console.log("visitUserMessages called",{username:e,currentUrl:window.location.href}),await this.delay(8e3);let o=null;const n=['div[role="button"]',"button",'a[role="button"]',"header button",'header div[role="button"]',"article button",'article div[role="button"]','[data-testid*="message"]','[data-testid*="send"]'],a=[];for(const r of n){const i=document.querySelectorAll(r);a.push(...Array.from(i))}const c=Array.from(new Set(a));console.log(`Found ${c.length} potential buttons to check for "Message" text.`);const d=c.map(r=>{var i;return{text:((i=r.innerText)==null?void 0:i.trim())||"",ariaLabel:r.getAttribute("aria-label"),title:r.getAttribute("title"),className:r.className,tagName:r.tagName,testId:r.getAttribute("data-testid")}}).filter(r=>r.text.length>0||r.ariaLabel||r.testId);console.log("All button details found:",d);const m=["message","wiadomość","napisz","send message","wyślij wiadomość","dm","direct","chat"];for(const r of c){const i=(r.innerText||"").toLowerCase().trim(),g=(r.getAttribute("aria-label")||"").toLowerCase(),l=(r.getAttribute("title")||"").toLowerCase(),u=(r.getAttribute("data-testid")||"").toLowerCase(),h=[i,g,l,u].join(" ");for(const y of m)if(h.includes(y)){o=r,console.log("Found message button by pattern:",{pattern:y,buttonText:i,ariaLabel:g,title:l,testId:u,element:r});break}if(o)break}if(o){await this.delay(4e3),console.log("visitUserMessages: Attempting to click message_button:",o);try{if(o.click(),await this.delay(5e3),window.location.href.includes("/direct/"))console.log("✅ Successfully navigated to DM page via regular click");else{console.log("Regular click failed, trying event dispatch");const r=new MouseEvent("click",{bubbles:!0,cancelable:!0,view:window});if(o.dispatchEvent(r),await this.delay(5e3),window.location.href.includes("/direct/"))console.log("✅ Successfully navigated to DM page via event dispatch");else{console.log("Event dispatch failed, trying focus + Enter"),o.focus(),await this.delay(2e3);const i=new KeyboardEvent("keydown",{key:"Enter",code:"Enter",keyCode:13,which:13,bubbles:!0});o.dispatchEvent(i),await this.delay(5e3),window.location.href.includes("/direct/")?console.log("✅ Successfully navigated to DM page via Enter key"):console.log("❌ All click methods failed - DM page not opened")}}}catch(r){console.log("❌ Error clicking message button:",r)}console.log("visitUserMessages: After message button interaction",{finalUrl:window.location.href})}else{console.log("❌ visitUserMessages: Direct message button not found. Trying three-dots menu approach...");let r=null;for(const i of c){const g=(i.getAttribute("aria-label")||"").toLowerCase(),l=((s=(t=i.querySelector("svg title"))==null?void 0:t.textContent)==null?void 0:s.toLowerCase())||"";if(g.includes("opcje")||g.includes("options")||l.includes("opcje")||l.includes("options")){r=i,console.log("Found options button:",{ariaLabel:g,svgTitle:l,element:i});break}}if(r){console.log("📱 Clicking options button to reveal message option...");try{r.click(),await this.delay(5e3);const i=document.querySelectorAll('button, div[role="button"]');let g=null;for(const l of i){const u=(l.innerText||"").toLowerCase().trim();if(u==="wyślij wiadomość"||u.includes("send message")||u.includes("message")||u==="wiadomość"){g=l,console.log("Found message button in dropdown menu:",{text:u,exactText:l.innerText,element:l});break}}if(g){console.log("📱 Clicking message button from dropdown menu..."),g.click();let l=!1;for(let u=1;u<=5;u++)if(await this.delay(6e3),window.location.href.includes("/direct/")){console.log("✅ Successfully navigated to DM page via dropdown menu"),l=!0;break}else if(console.log(`❌ Attempt ${u}/5 - Dropdown menu click failed, URL: ${window.location.href}`),u<5){console.log("🔄 Retrying with direct message button approach...");const h=document.querySelector('div[aria-label*="Message"], div[aria-label*="Wiadomość"], a[aria-label*="Message"], a[aria-label*="Wiadomość"]');if(h)console.log("Found direct message button, clicking..."),h.click();else{const y=document.querySelectorAll('button, div[role="button"]'),S=Array.from(y).find(E=>{var b;const k=((b=E.textContent)==null?void 0:b.toLowerCase())||"";return k.includes("message")||k.includes("wiadomość")||k.includes("wyślij wiadomość")});S&&(console.log("🔄 Retrying with found message button:",S.textContent),S.click())}}if(!l)return console.log("❌ Failed to navigate to DM page after 5 attempts via dropdown menu"),{success:!1,error:"Failed to navigate to DM page after 5 attempts via dropdown menu"}}else{console.log("❌ Message button not found in dropdown menu");const l=Array.from(i).map(u=>({text:(u.innerText||"").trim(),ariaLabel:u.getAttribute("aria-label"),className:u.className})).filter(u=>u.text.length>0);return console.log("Available buttons in dropdown menu:",l),{success:!1,error:"Message button not found in dropdown menu"}}}catch(i){return console.log("❌ Error interacting with options menu:",i),{success:!1,error:`Error interacting with options menu: ${i}`}}}else return console.log("❌ Options button not found either. Available button details:",d),{success:!1,error:"Message button not found on profile"}}return console.log("visitUserMessages completed",{username:e,finalUrl:window.location.href}),{success:!0}}catch(o){return console.error(`❌ Error visiting messages for @${e}:`,o),{success:!1,error:o.message}}}async navigateToProfile(e){const{username:t}=e,s=3;for(let o=1;o<=s;o++)try{console.log(`📍 Navigating to profile: @${t} (Attempt ${o}/${s})`),console.log(`📍 Current URL: ${window.location.href}`);const n=`https://instagram.com/${t}`;if(window.location.href.includes(`/${t}`))return console.log("✅ Already on target profile page"),{success:!0};if(console.log(`📍 Navigation needed - going to: ${n}`),window.location.href=n,await this.waitForNavigation(`/${t}`))return console.log("✅ Navigation completed"),{success:!0};throw new Error("Navigation timeout - profile page did not load")}catch(n){if(console.error(`❌ Navigation attempt ${o} failed:`,n),o<s)console.log("🔄 Refreshing page and retrying in 5 seconds..."),window.location.reload(),await this.delay(5e3);else return console.error("❌ All navigation attempts failed"),{success:!1,error:n instanceof Error?n.message:"Unknown error"}}return{success:!1,error:"Max retries exceeded"}}async waitForNavigation(e,t=1e4){return new Promise(s=>{const o=()=>{if(window.location.pathname.includes(e)){s(!0);return}};o();const n=setInterval(o,100);setTimeout(()=>{clearInterval(n),s(!1)},t)})}delay(e){return new Promise(t=>setTimeout(t,e))}}class T{constructor(){p(this,"PAGE_COUNT",24);p(this,"MIN_DELAY_MS",2e3);p(this,"MAX_DELAY_MS",5e3)}sleep(e){return new Promise(t=>setTimeout(t,e))}getRandomDelay(e=0){let t=this.MIN_DELAY_MS,s=this.MAX_DELAY_MS;return e>=3e3?(t=4e3,s=8e3):e>=1e3&&(t=3e3,s=6e3),t+Math.random()*(s-t)}getDynamicBatchSize(e){return e>=1e3?12:this.PAGE_COUNT}shouldTakeLongBreak(e){return e>0&&e%500===0}getHeaders(){return{"X-IG-App-ID":"936619743392459","X-Requested-With":"XMLHttpRequest",Referer:location.href}}async fetchJSON(e){try{const t=await fetch(e,{credentials:"include",headers:this.getHeaders()});if(!t.ok)throw new Error(`HTTP ${t.status} ${t.statusText}`);const s=await t.text();return s.startsWith("<!DOCTYPE")||s.includes("<html")?(console.log("⚠️ Got HTML response, trying injected script method..."),this.fetchViaInjectedScript(e)):JSON.parse(s)}catch(t){return console.log("⚠️ Direct fetch failed, trying injected script method...",t),this.fetchViaInjectedScript(e)}}fetchViaInjectedScript(e){return new Promise((t,s)=>{const o=`api_request_${Date.now()}_${Math.random()}`,n=c=>{c.data.type==="API_RESPONSE"&&c.data.requestId===o&&(window.removeEventListener("message",n),c.data.success?t(c.data.data):s(new Error(c.data.error)))};window.addEventListener("message",n);const a=document.createElement("script");a.textContent=`
        (async () => {
          try {
            const response = await fetch('${e}', {
              credentials: 'include',
              headers: {
                'X-IG-App-ID': '936619743392459',
                'X-Requested-With': 'XMLHttpRequest',
                'Referer': location.href
              }
            });
            
            const text = await response.text();
            
            if (text.startsWith('<!DOCTYPE') || text.includes('<html')) {
              throw new Error('Got HTML response instead of JSON - authentication may be required');
            }
            
            const data = JSON.parse(text);
            
            window.postMessage({
              type: 'API_RESPONSE',
              requestId: '${o}',
              success: true,
              data: data
            }, '*');
          } catch (error) {
            window.postMessage({
              type: 'API_RESPONSE',
              requestId: '${o}',
              success: false,
              error: error.message
            }, '*');
          }
        })();
      `,document.head.appendChild(a),a.remove(),setTimeout(()=>{window.removeEventListener("message",n),s(new Error("API request timeout"))},1e4)})}async getUserIdByUsername(e){var n,a;const t=`https://www.instagram.com/api/v1/users/web_profile_info/?username=${encodeURIComponent(e)}`,s=await this.fetchJSON(t),o=(a=(n=s==null?void 0:s.data)==null?void 0:n.user)==null?void 0:a.id;if(!o)throw new Error(`Could not get user ID for username: ${e}`);return console.log(`✅ Got user ID for @${e}: ${o}`),o}async openFollowersModal(e){try{console.log(`📱 Opening followers modal for @${e}...`);const t=window.location.pathname,s=`/${e}/`;if(!t.includes(e))return console.log(`📍 Not on ${e}'s profile. Current: ${t}`),window.location.href=`https://www.instagram.com/${e}/`,await this.sleep(3e3),!1;const o=document.querySelector('a[href*="/followers/"]');if(o)o.click();else{console.log("❌ Followers link not found on page");const a=Array.from(document.querySelectorAll("a")).find(c=>{var d;return(d=c.textContent)==null?void 0:d.includes("follower")});if(a)a.click();else return console.log("❌ No followers link found at all"),!1}return console.log("⏳ Waiting for modal to open..."),await this.sleep(2e3),document.querySelector('div[role="dialog"]')?(console.log("✅ Followers modal opened successfully"),!0):(console.log("⚠️ Modal not detected, but continuing anyway"),!0)}catch(t){return console.error("❌ Error opening followers modal:",t),!1}}async scrapeFollowersAPI(e){const{count:t,username:s,startMaxId:o}=e;try{console.log(`🚀 Starting hybrid follower scraping for @${s} (target: ${t})`),console.log("📍 Current page:",window.location.href),console.log("🍪 Cookies available:",document.cookie?"Yes":"No"),await this.openFollowersModal(s)||console.log("⚠️ Modal opening failed, but continuing with API...");const a=await this.getUserIdByUsername(s),{followers:c,nextMaxId:d}=await this.fetchFollowersByUserId(a,t,o),m=this.formatFollowersForDatabase(c),r=document.querySelector('div[role="dialog"] button[aria-label="Close"]');return r&&(r.click(),console.log("📱 Modal closed")),console.log(`✅ Hybrid scraping completed! Collected ${m.length} followers`),{success:!0,followers:m,nextMaxId:d}}catch(n){return console.error("❌ Hybrid scraping failed:",n),{success:!1,error:n instanceof Error?n.message:"Unknown error occurred"}}}async fetchFollowersByUserId(e,t,s){let o=s||null;const n=[];for(console.log(`📡 Starting follower fetch for user ID: ${e} (limit: ${t})`);n.length<t;){const a=this.getDynamicBatchSize(n.length),c=new URL(`https://www.instagram.com/api/v1/friendships/${e}/followers/`);c.searchParams.set("count",String(a)),c.searchParams.set("search_surface","follow_list_page"),o&&c.searchParams.set("max_id",o),console.log(`📡 Fetching batch... (current: ${n.length}/${t}, batch size: ${a})`);const d=await this.fetchJSON(c.toString()),m=Array.isArray(d==null?void 0:d.users)?d.users:[];if(m.length===0){console.log("📭 No more followers available");break}n.push(...m),console.log(`📊 Batch complete: +${m.length} followers (total: ${n.length})`);try{chrome.runtime.sendMessage({action:"SCRAPING_PROGRESS_UPDATE",data:{current:n.length,total:t}})}catch(r){console.log("Could not send progress update:",r)}if(!(d!=null&&d.next_max_id)){console.log("📄 Reached last page");break}if(o=d.next_max_id,this.shouldTakeLongBreak(n.length)){const r=1e4+Math.random()*1e4;console.log(`☕ Taking a long break (${Math.round(r/1e3)}s) at ${n.length} followers...`),await this.sleep(r)}else{const r=this.getRandomDelay(n.length);console.log(`⏳ Waiting ${Math.round(r)}ms before next batch...`),await this.sleep(r)}}return{followers:n.slice(0,t),nextMaxId:o}}formatFollowersForDatabase(e){const t=Date.now();return e.map((s,o)=>{const n=new Date(t-o*6e4),a={instagramUserId:s.pk||s.pk_id||s.id,nickname:s.username||"unknown_user",profilePhoto:s.profile_pic_url||void 0,collectedAt:n.toISOString()};return console.log(`✅ Formatted follower #${o+1}: ${a.nickname} (ID: ${a.instagramUserId})`),a})}}class C{async checkNewFollowers(){var e,t;try{const s=new Date().toLocaleTimeString();console.log(`🔍 [${s}] ➤ FOLLOWER CHECK STARTED - Checking Instagram Activity Inbox for new followers...`);const o=await this.getCsrfToken();if(!o)throw new Error("Could not get CSRF token");const n=await fetch("https://i.instagram.com/api/v1/news/inbox/",{credentials:"include",headers:{accept:"application/json, text/plain, */*",Referer:"https://www.instagram.com/","Referrer-Policy":"strict-origin-when-cross-origin","x-asbd-id":"129477","X-IG-App-ID":"936619743392459","x-instagram-ajax":"1","X-CSRFToken":o,"x-requested-with":"XMLHttpRequest"},body:null,method:"POST"});if(!n.ok)throw new Error(`Instagram API error: ${n.status}`);const a=await n.json();if(a.status!=="ok")throw new Error(`Instagram API returned status: ${a.status}`);const c=a.new_stories||[];console.log(`🔍 [${s}] DEBUG: Found ${c.length} total stories in inbox`),console.log(`🔍 [${s}] DEBUG: First few stories:`,c.slice(0,3));const d=[];for(const r of c)if(console.log(`🔍 [${s}] DEBUG: Processing story - story_type: ${r.story_type}, type: ${r.type}`),r.story_type===101&&r.type===3){console.log(`✅ [${s}] DEBUG: Found NEW FOLLOWER story`);let i=null,g=null;if(r.args){if(r.args.profile_name&&(i=r.args.profile_name),r.args.profile_id&&(g=r.args.profile_id),!i&&r.args.links&&Array.isArray(r.args.links)){for(const l of r.args.links)if(l&&l.type==="user"){i=l.username||l.name,g=l.id||l.user_id;break}}if(!i&&r.args.rich_text){const l=r.args.rich_text.match(/\{([^|]+)\|[^|]+\|[^|]+\|user\?id=(\d+)\}/);l&&(i=l[1],g=l[2])}}if(!i&&r.args&&r.args.destination){const l=r.args.destination.match(/username=([^&]+)/),u=r.args.destination.match(/id=([^&]+)/);if(l&&(i=l[1]),!g&&u&&(g=u[1]),!g){const h=r.args.destination.match(/clips_home\?id=([^&]+)/);h&&(g=h[1])}}if(!i&&r.args&&r.args.text){let l=r.args.text.match(/^([a-zA-Z0-9._]+)\s/);l||(l=r.args.text.match(/Użytkownik\s+([a-zA-Z0-9._]+)\s/)),l&&(i=l[1])}console.log(`✅ [${s}] DEBUG: Found NEW FOLLOWER @${i} (ID: ${g})`),i&&g?d.push({instagramUserId:i,nickname:i,instagramId:g,timestamp:Math.floor(Date.now()/1e3),followedAt:new Date,collectedAt:new Date().toISOString()}):console.log(`⚠️ [${s}] DEBUG: Could not extract username/ID from story:`,{hasArgs:!!r.args,destination:(e=r.args)==null?void 0:e.destination,text:(t=r.args)==null?void 0:t.text,extractedUsername:i,extractedUserId:g})}else console.log(`🔍 [${s}] DEBUG: Skipped story - not a new follower (story_type: ${r.story_type}, type: ${r.type})`);const m=new Date().toLocaleTimeString();return console.log(`✅ [${m}] FOUND ${d.length} NEW FOLLOWERS - Check completed`),{success:!0,followers:d}}catch(s){return console.error("❌ Error checking new followers:",s),{success:!1,error:s.message}}}async getCsrfToken(){try{const e=document.querySelector('meta[name="csrf-token"]');if(e){const o=e.getAttribute("content");if(o)return console.log("✅ Got CSRF token from meta tag"),o}const t=document.cookie.split(";");for(const o of t){const[n,a]=o.trim().split("=");if(n==="csrftoken")return console.log("✅ Got CSRF token from cookie"),a}const s=document.querySelectorAll("script:not([src])");for(const o of s){const a=(o.textContent||"").match(/"csrf_token":"([^"]+)"/);if(a)return console.log("✅ Got CSRF token from script"),a[1]}return console.log("❌ Could not find CSRF token"),null}catch(e){return console.error("❌ Error getting CSRF token:",e),null}}}class L{constructor(){p(this,"messageSender");p(this,"profileNavigator");p(this,"apiFollowerScraper");p(this,"activityMonitor");this.messageSender=new x,this.profileNavigator=new D,this.apiFollowerScraper=new T,this.activityMonitor=new C,this.init()}init(){this.messageSender.initializeInjectedScript(),chrome.runtime.onMessage.addListener((e,t,s)=>(this.handleMessage(e,t,s),!0)),console.log("AIChromaPRO Instagram Content Script loaded")}handleMessage(e,t,s){switch(console.log("📨 Content script received:",e.action),e.action){case"PING":s({success:!0});break;case"SCRAPE_FOLLOWERS":return console.log("📨 Content script received SCRAPE_FOLLOWERS"),this.apiFollowerScraper.scrapeFollowersAPI(e).then(s),!0;case"CHECK_ZERO_MESSAGES":return console.log(`📨 [${new Date().toLocaleTimeString()}] CHECK_ZERO_MESSAGES request received for @${e.username}`),this.messageSender.getHaveZeroMessages(e.username).then(o=>{s({success:!0,result:o})}).catch(o=>{s({success:!1,error:o instanceof Error?o.message:"Unknown error"})}),!0;case"CHECK_NEW_FOLLOWERS":return console.log(`📨 [${new Date().toLocaleTimeString()}] CHECK_NEW_FOLLOWERS request received`),this.activityMonitor.checkNewFollowers().then(s),!0;case"SEND_MESSAGE":return this.messageSender.sendMessage(e).then(s),!0;case"VISIT_USER_MESSAGES":return this.profileNavigator.visitUserMessages(e.username).then(s),!0;case"VERIFY_MESSAGE_IN_DOM":return console.log(`📨 [${new Date().toLocaleTimeString()}] VERIFY_MESSAGE_IN_DOM request received for @${e.username}`),this.messageSender.verifyMessageInDOM(e.username,e.expectedMessage).then(o=>{s({success:!0,messageFound:o})}).catch(o=>{s({success:!1,messageFound:!1,error:o instanceof Error?o.message:"Unknown error"})}),!0;default:s({error:"Unknown action"})}}}new L;
