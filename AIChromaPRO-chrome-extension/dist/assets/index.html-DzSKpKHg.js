var p=Object.defineProperty;var h=(l,e,t)=>e in l?p(l,e,{enumerable:!0,configurable:!0,writable:!0,value:t}):l[e]=t;var u=(l,e,t)=>h(l,typeof e!="symbol"?e+"":e,t);import{b as c}from"./browser-polyfill-DQnhcI3D.js";(function(){const e=document.createElement("link").relList;if(e&&e.supports&&e.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))o(n);new MutationObserver(n=>{for(const s of n)if(s.type==="childList")for(const a of s.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&o(a)}).observe(document,{childList:!0,subtree:!0});function t(n){const s={};return n.integrity&&(s.integrity=n.integrity),n.referrerPolicy&&(s.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?s.credentials="include":n.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function o(n){if(n.ep)return;n.ep=!0;const s=t(n);fetch(n.href,s)}})();class f{constructor(){u(this,"settings",null);u(this,"state",null);this.init()}async init(){try{console.log("🔍 Debug: Initializing popup..."),await this.loadData(),this.setupEventListeners(),console.log("🔍 Debug: Updating UI with loaded data..."),this.updateUI(),this.hideLoadingScreen(),console.log("🔍 Debug: Popup initialization complete")}catch(e){console.error("🔍 Debug: Error during initialization:",e),this.hideLoadingScreen()}}async loadData(){try{console.log("🔍 Debug: Loading data from storage...");const e=await c.storage.local.get(["settings"]);console.log("🔍 Debug: Stored data:",e),e.settings?(this.settings=e.settings,console.log("🔍 Debug: Settings loaded successfully:",{hasApiKey:!!this.settings.apiKey,baseUrl:this.settings.baseUrl,maxActions:this.settings.maxActionsPerDay})):(console.log("🔍 Debug: No settings found in storage"),this.settings=null);try{const t=await c.runtime.sendMessage({action:"GET_STATE"});console.log("🔍 Debug: State response:",t),t!=null&&t.state?this.state=t.state:this.state={isRunning:!1,todayActions:0,pendingMessages:0}}catch(t){console.log("🔍 Debug: Could not load state from background script:",t),this.state={isRunning:!1,todayActions:0,pendingMessages:0}}}catch(e){console.error("🔍 Debug: Error loading data:",e),this.settings=null,this.state={isRunning:!1,todayActions:0,pendingMessages:0}}}setupEventListeners(){const e=document.getElementById("start-btn"),t=document.getElementById("stop-btn");e==null||e.addEventListener("click",()=>this.startExtension()),t==null||t.addEventListener("click",()=>this.stopExtension());const o=document.getElementById("save-settings");o==null||o.addEventListener("click",()=>this.saveSettings());const n=document.getElementById("open-dashboard");n==null||n.addEventListener("click",()=>this.openDashboard()),setInterval(()=>this.refreshState(),5e3)}hideLoadingScreen(){const e=document.getElementById("loading-screen"),t=document.getElementById("main-content");e&&(e.style.display="none"),t&&(t.style.display="block")}updateUI(){this.updateStatusIndicator(),this.updateStats(),this.updateScrapingProgress(),this.updateSettingsForm(),this.updateButtons()}updateStatusIndicator(){var o;const e=document.getElementById("status-dot"),t=document.getElementById("status-text");(o=this.state)!=null&&o.isRunning?(e==null||e.classList.add("active"),t&&(t.textContent="Running")):(e==null||e.classList.remove("active"),t&&(t.textContent="Stopped"))}updateStats(){var s;if(!this.state)return;const e=document.getElementById("today-actions"),t=document.getElementById("pending-messages"),o=document.getElementById("collected-followers"),n=document.getElementById("daily-limit");if(e&&(e.textContent=this.state.todayActions.toString()),t&&(t.textContent=this.state.pendingMessages.toString()),n&&this.settings&&(n.textContent=this.settings.maxActionsPerDay.toString()),o){const a=((s=this.state.scrapingProgress)==null?void 0:s.current)||0;o.textContent=a.toString()}}updateScrapingProgress(){var r,i,d;if(!this.state)return;const e=document.getElementById("scraping-progress-card"),t=document.getElementById("scraping-progress"),o=document.getElementById("progress-fill"),n=document.getElementById("status-text"),s=!!((r=this.settings)!=null&&r.apiKey);if(this.state.scrapingStatus==="INITIAL_SCRAPING"&&this.state.isRunning&&s&&e){e.style.display="block";const m=((i=this.state.scrapingProgress)==null?void 0:i.current)||0,g=((d=this.state.scrapingProgress)==null?void 0:d.target)||5e3;if(t&&(t.textContent=`${m} / ${g.toLocaleString()}`),o){const y=g>0?Math.min(m/g*100,100):0;o.style.width=`${y}%`}n&&(n.textContent="Collecting Followers")}else e&&(e.style.display="none")}updateSettingsForm(){var n,s;console.log("🔍 Debug: Updating settings form..."),console.log("🔍 Debug: Has settings:",!!this.settings),console.log("🔍 Debug: Has API key:",!!((n=this.settings)!=null&&n.apiKey));const e=document.getElementById("api-key-section"),t=document.getElementById("connected-section"),o=document.getElementById("api-key");(s=this.settings)!=null&&s.apiKey?(console.log("🔍 Debug: Showing connected state"),e&&(e.style.display="none",console.log("🔍 Debug: Hidden API key section")),t&&(t.style.display="block",console.log("🔍 Debug: Shown connected section")),this.loadSettingsIntoForm()):(console.log("🔍 Debug: Showing API key input state"),e&&(e.style.display="block",console.log("🔍 Debug: Shown API key section")),t&&(t.style.display="none",console.log("🔍 Debug: Hidden connected section")),o&&(o.value=""))}loadSettingsIntoForm(){if(!this.settings)return;const e=document.getElementById("display-max-actions"),t=document.getElementById("display-dm-range"),o=document.getElementById("display-break-range"),n=document.getElementById("display-delay-between-dms"),s=document.getElementById("display-stop-time");e&&(e.textContent=this.settings.maxActionsPerDay.toString()),t&&(t.textContent=`${this.settings.dmBeforeBreakMin}-${this.settings.dmBeforeBreakMax}`),o&&(o.textContent=`${this.settings.breakTimeMin}-${this.settings.breakTimeMax} min`),n&&(n.textContent=`${this.settings.delayBetweenDmsMin}-${this.settings.delayBetweenDmsMax} min`),s&&(s.textContent=`${this.settings.naturalStopStart}-${this.settings.naturalStopEnd}`)}updateButtons(){var n,s,a;const e=document.getElementById("start-btn"),t=document.getElementById("stop-btn"),o=((n=this.settings)==null?void 0:n.apiKey)&&!((s=this.state)!=null&&s.isRunning);(a=this.state)!=null&&a.isRunning?(e&&(e.style.display="none"),t&&(t.style.display="block")):(e&&(e.style.display="block",e.disabled=!o),t&&(t.style.display="none"))}async startExtension(){var e;if(!((e=this.settings)!=null&&e.apiKey)){this.showError("Please configure API key first");return}try{this.showLoadingMessage("Connecting to dashboard...");const t=await c.runtime.sendMessage({action:"START_EXTENSION"});if(this.hideMessages(),t.success)t.scrapingStatus==="INITIAL_SCRAPING"?this.showSuccess("Extension started - Beginning follower collection"):this.showSuccess("Extension started - Ready for automation"),await this.refreshState();else{const o=t.error||"Failed to start extension";this.showError(o),console.error("❌ Start extension failed:",t)}}catch(t){this.hideMessages(),console.error("❌ Extension communication error:",t),this.showError("Failed to communicate with extension background")}}async stopExtension(){try{const e=await c.runtime.sendMessage({action:"STOP_EXTENSION"});e.success?(this.showSuccess("Extension stopped"),await this.refreshState()):this.showError(e.error||"Failed to stop extension")}catch{this.showError("Failed to stop extension")}}async saveSettings(){var o;const e=document.getElementById("api-key"),t=(o=e==null?void 0:e.value)==null?void 0:o.trim();if(!t){this.showError("Please enter API key");return}try{const n="https://app.setorai.com";console.log("🔍 Debug: Base URL:",n),console.log("🔍 Debug: API Key:",t.substring(0,10)+"...");const s={apiKey:t,baseUrl:n.replace(/\/$/,""),isActive:!0,maxActionsPerDay:50,dmBeforeBreakMin:7,dmBeforeBreakMax:10,breakTimeMin:30,breakTimeMax:60,delayBetweenDmsMin:5,delayBetweenDmsMax:15,naturalStopStart:"09:00",naturalStopEnd:"21:00"},a=`${s.baseUrl}/api/chrome-extension/settings?apiKey=${s.apiKey}`;console.log("🔍 Debug: Test URL:",a),console.log("🔍 Debug: Starting fetch request...");const r=await fetch(a,{method:"GET",mode:"cors",headers:{"Content-Type":"application/json"}});if(console.log("🔍 Debug: Response status:",r.status),console.log("🔍 Debug: Response headers:",Object.fromEntries(r.headers.entries())),!r.ok){const d=await r.text();throw console.log("🔍 Debug: Error response body:",d),new Error(`API error ${r.status}: ${d}`)}console.log("🔍 Debug: Parsing JSON response...");const i=await r.json();console.log("🔍 Debug: API Settings received:",i),s.maxActionsPerDay=i.maxActionsPerDay||50,s.dmBeforeBreakMin=i.dmBeforeBreakMin||7,s.dmBeforeBreakMax=i.dmBeforeBreakMax||10,s.breakTimeMin=i.breakTimeMin||30,s.breakTimeMax=i.breakTimeMax||60,s.delayBetweenDmsMin=i.delayBetweenDmsMin||5,s.delayBetweenDmsMax=i.delayBetweenDmsMax||15,s.naturalStopStart=i.naturalStopStart||"09:00",s.naturalStopEnd=i.naturalStopEnd||"21:00",s.organizationId=i.organizationId,console.log("🔍 Debug: Final settings object:",s),await c.storage.local.set({settings:s}),s.organizationId&&await c.storage.sync.set({organizationId:s.organizationId}),await c.runtime.sendMessage({action:"UPDATE_SETTINGS",data:s}),this.settings=s,this.showSuccess("Settings saved successfully"),this.updateUI()}catch(n){console.error("🔍 Debug: Full error:",n),console.error("🔍 Debug: Error stack:",n instanceof Error?n.stack:"No stack");let s="Unknown error";n instanceof TypeError&&n.message.includes("Failed to fetch")?s="Network error - check if dashboard is running and accessible":n instanceof Error&&(s=n.message),this.showError(`Failed to save settings: ${s}`)}}async openDashboard(){var t;const e=((t=this.settings)==null?void 0:t.baseUrl)||"https://app.setorai.com";await c.tabs.create({url:`${e}/organizations`})}async refreshState(){try{const e=await c.runtime.sendMessage({action:"GET_STATE"});e!=null&&e.state&&(this.state=e.state,this.updateUI())}catch{}}showError(e){this.hideMessages();const t=document.getElementById("error-message");t&&(t.textContent=e,t.style.display="block",setTimeout(()=>{t.style.display="none"},5e3))}showSuccess(e){this.hideMessages();const t=document.getElementById("success-message");t&&(t.textContent=e,t.style.display="block",setTimeout(()=>{t.style.display="none"},3e3))}showLoadingMessage(e){this.hideMessages();const t=document.getElementById("success-message");t&&(t.textContent=`⏳ ${e}`,t.style.display="block",t.style.color="#888888")}hideMessages(){const e=document.getElementById("error-message"),t=document.getElementById("success-message");e&&(e.style.display="none"),t&&(t.style.display="none",t.style.color="#22c55e")}}document.addEventListener("DOMContentLoaded",()=>{new f});
