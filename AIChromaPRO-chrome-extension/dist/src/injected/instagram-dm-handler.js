// Instagram localStorage-based DM Handler (from working old extension)
// Based on reverse engineering findings: Instagram uses localStorage messaging queue

console.log('Loading localStorage-based Instagram DM handler');

// Prevent multiple instances
if (window.instagramLocalStorageHandler) {
    console.log('localStorage handler already loaded, skipping');
} else {
    window.instagramLocalStorageHandler = true;

    class InstagramLocalStorageMessenger {
        constructor() {
            this.initialized = false;
            this.messageQueue = new Map();
            this.originalSetItem = null;
            this.setupStorageHook();
        }

        // Hook into localStorage to monitor Instagram's messaging system
        setupStorageHook() {
            if (!this.originalSetItem) {
                this.originalSetItem = Storage.prototype.setItem;
                const self = this;

                Storage.prototype.setItem = function(key, value) {
                    // Monitor Instagram's message tracking
                    if (key === 'mw_sent_message') {
                        console.log('Instagram message sent detected:', value);
                        self.onInstagramMessageSent(value);
                    }

                    return self.originalSetItem.apply(this, [key, value]);
                };

                console.log('Storage hook installed successfully');
            }
        }

        // Called when Instagram sends a message (for monitoring)
        onInstagramMessageSent(timestamp) {
            console.log('Instagram internal message sent at:', timestamp);
        }

        // Initialize Instagram's messaging context
        async initialize() {
            try {
                console.log('Initializing Instagram localStorage messenger');
                this.initialized = true;
                console.log('localStorage messenger initialized');
                return true;
            } catch (error) {
                console.error('Failed to initialize localStorage messenger:', error);
                return false;
            }
        }

        // Main message sending function - mimics Instagram's localStorage approach
        async sendMessage(threadId, viewerId, user, text) {
            let heartbeatInterval;
            try {
                console.log(`Attempting localStorage-based message send to thread ${threadId}`);
                console.log(`Message: "${text}" to user: ${user.username} (${user.id})`);

                // Start heartbeat to keep content script informed
                this.startHeartbeat('initializing');
                heartbeatInterval = setInterval(() => {
                    this.sendHeartbeat('processing');
                }, 10000); // Send heartbeat every 10 seconds

                if (!this.initialized) {
                    this.sendHeartbeat('initializing');
                    await this.initialize();
                }

                // Method 1: Try to trigger Instagram's internal message sending
                this.sendHeartbeat('attempting Instagram internal send');
                const messageSuccess = await this.triggerInstagramMessageSend(threadId, text);
                if (messageSuccess) {
                    console.log('Message sent via Instagram internal system');
                    clearInterval(heartbeatInterval);
                    return this.sendSuccessResponse();
                }

                // Method 2: Try DOM manipulation approach
                this.sendHeartbeat('trying DOM manipulation');
                const domSuccess = await this.sendViaDOMManipulation(text);
                if (domSuccess) {
                    console.log('Message sent via DOM manipulation');
                    clearInterval(heartbeatInterval);
                    return this.sendSuccessResponse();
                }

                // Method 3: Try localStorage queue injection
                this.sendHeartbeat('trying localStorage injection');
                const queueSuccess = await this.injectIntoMessageQueue(threadId, text);
                if (queueSuccess) {
                    console.log('Message sent via queue injection');
                    clearInterval(heartbeatInterval);
                    return this.sendSuccessResponse();
                }

                throw new Error('All sending methods failed');

            } catch (error) {
                console.error('Error sending message:', error);
                if (heartbeatInterval) clearInterval(heartbeatInterval);
                return this.sendErrorResponse(error);
            }
        }

        // Send heartbeat to content script
        sendHeartbeat(status) {
            window.postMessage({
                type: 'INJECT_HEARTBEAT',
                status: status,
                timestamp: Date.now()
            }, '*');
        }

        // Convenience method to start heartbeat
        startHeartbeat(initialStatus) {
            this.sendHeartbeat(initialStatus || 'starting');
        }

        // Try to trigger Instagram's internal message sending mechanism
        async triggerInstagramMessageSend(threadId, text) {
            try {
                console.log('Attempting to trigger Instagram internal message send');
                this.sendHeartbeat('finding message input');

                const messageInput = this.findMessageInput();
                if (!messageInput) {
                    console.log('Message input not found');
                    return false;
                }

                this.sendHeartbeat('clearing and typing message');
                this.clearInput(messageInput);
                await this.delay(50);

                this.insertTextIntoInput(messageInput, text);
                await this.delay(200);

                this.sendHeartbeat('triggering send action');
                const sendTriggered = this.triggerSendAction(messageInput);
                if (sendTriggered) {
                    this.sendHeartbeat('waiting for Instagram confirmation');
                    const messageProcessed = await this.waitForMessageProcessing();
                    return messageProcessed;
                }

                return false;
            } catch (error) {
                console.log('Internal trigger failed:', error);
                return false;
            }
        }

        findMessageInput() {
            const selectors = [
                'div[contenteditable="true"][role="textbox"]',
                'div[aria-label*="Message"]',
                'div[aria-label*="Wiadomość"]', // Polish
                'div[data-testid="message-input"]',
                'textarea[placeholder*="Message.."]',
                'textarea[placeholder*="Wiadomość.."]',
            ];

            console.log('🔍 Looking for message input...');
            console.log('Current URL:', window.location.href);
            
            for (const selector of selectors) {
                const element = document.querySelector(selector);
                if (element) {
                    console.log('✅ Found message input with selector:', selector);
                    console.log('Element details:', {
                        tagName: element.tagName,
                        className: element.className,
                        id: element.id,
                        ariaLabel: element.getAttribute('aria-label'),
                        placeholder: element.getAttribute('placeholder')
                    });
                    return element;
                }
            }

            // Debug: Show what elements are actually on the page
            const allInputs = document.querySelectorAll('input, textarea, div[contenteditable], div[role="textbox"]');
            console.log('❌ No message input found. Available input elements:', 
                Array.from(allInputs).map(el => ({
                    tagName: el.tagName,
                    className: el.className,
                    id: el.id,
                    role: el.getAttribute('role'),
                    ariaLabel: el.getAttribute('aria-label'),
                    placeholder: el.getAttribute('placeholder'),
                    contentEditable: el.getAttribute('contenteditable')
                }))
            );
            
            // Check if we're on a profile page instead of DM page
            if (!window.location.href.includes('/direct/')) {
                console.log('⚠️ Not on DM page - still on profile. URL:', window.location.href);
                console.log('⚠️ This indicates message button click failed to navigate to DM conversation');
                return null;
            }
            
            console.log('❌ Message input not found - returning null (no refresh during active sending)');
            return null;
        }

        clearInput(element) {
            try {
                element.textContent = '';
                element.innerHTML = '';
                element.dispatchEvent(new Event('input', { bubbles: true }));
                element.dispatchEvent(new Event('change', { bubbles: true }));
                console.log('Input cleared');
            } catch (error) {
                console.log('Failed to clear input:', error);
            }
        }

        insertTextIntoInput(element, text) {
            try {
                element.focus();
                document.execCommand('insertText', false, text);
                element.dispatchEvent(new Event('input', { bubbles: true }));
                return true;
            } catch (error) {
                console.log('Failed to insert text:', error);
                return false;
            }
        }

        triggerSendAction(messageInput) {
            try {
                const sendButton = this.findSendButton();
                if (sendButton) {
                    sendButton.click();
                    return true;
                }
                
                // Fallback to Enter key
                const enterEvent = new KeyboardEvent('keydown', {
                    key: 'Enter',
                    code: 'Enter',
                    keyCode: 13,
                    which: 13,
                    bubbles: true,
                    cancelable: true
                });
                messageInput.dispatchEvent(enterEvent);
                return true;
            } catch (error) {
                console.log('Failed to trigger send:', error);
                return false;
            }
        }

        findSendButton() {
            console.log('🔍 Looking for send button...');
            
            const selectors = [
                'button[type="submit"]',
                'div[role="button"]:has(svg[aria-label="Send"])',
                'div[role="button"]:has(svg[aria-label="Wyślij"])'
            ];

            for (const selector of selectors) {
                try {
                    const element = document.querySelector(selector);
                    if (element) {
                        console.log('✅ Found send button with selector:', selector);
                        return element;
                    }
                } catch (e) {
                    // Ignore invalid selectors like :has() on older browsers
                    console.log('Invalid selector:', selector, e.message);
                }
            }
            
            // Fallback for text-based buttons
            const allButtons = document.querySelectorAll('div[role="button"], button');
            console.log('Checking', allButtons.length, 'buttons for send text...');
            
            for(const btn of allButtons) {
                const buttonText = btn.innerText.toLowerCase().trim();
                if(buttonText === 'send' || buttonText === 'wyślij') {
                    console.log('✅ Found send button by text:', buttonText);
                    return btn;
                }
            }

            // Debug: Show available buttons
            console.log('❌ No send button found. Available buttons:', 
                Array.from(allButtons).map(btn => ({
                    tagName: btn.tagName,
                    type: btn.getAttribute('type'),
                    role: btn.getAttribute('role'),
                    text: btn.innerText.trim(),
                    ariaLabel: btn.getAttribute('aria-label')
                })).filter(btn => btn.text || btn.ariaLabel)
            );

            return null;
        }

        async waitForMessageProcessing(timeout = 10000) {
            return new Promise((resolve) => {
                const startTime = Date.now();
                const checkInterval = setInterval(() => {
                    const currentTime = Date.now();
                    
                    // Method 1: Check localStorage (original method)
                    const lastMessageTime = localStorage.getItem('mw_sent_message');
                    if (lastMessageTime && parseInt(lastMessageTime) > startTime) {
                        clearInterval(checkInterval);
                        console.log('✅ Message processing detected in localStorage');
                        resolve(true);
                        return;
                    }

                    // Method 2: Visual DOM verification - check if message appears in conversation
                    if (this.checkMessageAppearedInDOM()) {
                        clearInterval(checkInterval);
                        console.log('✅ Message processing detected via DOM verification');
                        resolve(true);
                        return;
                    }

                    // Method 3: Check if input was cleared (indicates message was processed)
                    if (this.checkInputCleared()) {
                        clearInterval(checkInterval);
                        console.log('✅ Message processing detected via input clear verification');
                        resolve(true);
                        return;
                    }

                    if (currentTime - startTime > timeout) {
                        clearInterval(checkInterval);
                        console.log('⚠️ Message processing timeout (possible false negative)');
                        resolve(false);
                    }
                }, 200); // Check every 200ms for better responsiveness
            });
        }

        // Check if a new message appeared in the conversation DOM
        checkMessageAppearedInDOM() {
            try {
                // Look for recent message elements that appeared after send attempt
                const messageElements = document.querySelectorAll('[data-scope="messages_table"] > div, [role="log"] [data-testid*="message"], .x1n2onr6 > div[role="presentation"]');
                
                // Check if any message elements contain recent timestamps or indicators
                for (const element of messageElements) {
                    const elementText = element.innerText || element.textContent || '';
                    
                    // Look for "now" timestamp or very recent time indicators
                    if (elementText.includes('now') || elementText.includes('teraz') || 
                        elementText.includes('just now') || elementText.includes('właśnie')) {
                        console.log('🔍 Found recent message timestamp in DOM');
                        return true;
                    }
                }
                
                return false;
            } catch (error) {
                console.log('DOM verification error (non-critical):', error);
                return false;
            }
        }

        // Check if message input was cleared (indicates Instagram processed the send)
        checkInputCleared() {
            try {
                const messageInput = this.findMessageInput();
                if (messageInput) {
                    const inputValue = messageInput.innerText || messageInput.textContent || messageInput.value || '';
                    const isEmpty = inputValue.trim().length === 0;
                    
                    if (isEmpty) {
                        console.log('🔍 Message input is cleared - likely sent successfully');
                        return true;
                    }
                }
                return false;
            } catch (error) {
                console.log('Input clear verification error (non-critical):', error);
                return false;
            }
        }

        async sendViaDOMManipulation(text) {
            return false;
        }

        async injectIntoMessageQueue(threadId, text) {
            try {
                const timestamp = Date.now().toString();
                localStorage.setItem('mw_sent_message', timestamp);
                this.triggerMessageProcessing();
                return true;
            } catch (error) {
                console.log('Queue injection failed:', error);
                return false;
            }
        }

        triggerMessageProcessing() {
            try {
                window.dispatchEvent(new Event('storage'));
                window.dispatchEvent(new CustomEvent('instagramMessageSent'));
            } catch (error) {
                console.log('Failed to trigger processing:', error);
            }
        }

        delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        sendSuccessResponse() {
            window.postMessage({
                type: 'INJECT_DISPATCH_DM_RESPONSE',
                ret: 1,
                status_code: 200,
                timestamp: Date.now()
            }, '*');
        }

        sendErrorResponse(error) {
            window.postMessage({
                type: 'INJECT_DISPATCH_DM_RESPONSE',
                ret: 0,
                status_code: 500,
                error: error.message
            }, '*');
        }
    }

    const dmMessenger = new InstagramLocalStorageMessenger();

    window.addEventListener('message', async (event) => {
        if (event.data && event.data.type === 'INJECT_DISPATCH_DM_REQUEST') {
            console.log('Received DM request:', event.data);
            const { thread_id, viewer_id, user, text } = event.data;
            await dmMessenger.sendMessage(thread_id, viewer_id, user, text);
        }
    });

    console.log('Instagram localStorage DM handler initialized');
}