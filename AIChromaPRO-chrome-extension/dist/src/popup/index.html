<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AIChromaPRO Instagram Automation</title>
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      width: 350px;
      min-height: 500px;
      background: #000000;
      color: #ffffff;
    }

    .header {
      background: #111111;
      color: white;
      padding: 20px;
      text-align: center;
      border-bottom: 1px solid #333333;
    }

    .header h1 {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 8px;
    }

    .header p {
      font-size: 14px;
      opacity: 0.9;
    }

    .content {
      padding: 20px;
    }

    .status-card {
      background: #111111;
      border: 1px solid #333333;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 20px;
    }

    .status-indicator {
      display: flex;
      align-items: center;
      gap: 8px;
      margin-bottom: 12px;
    }

    .status-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: #ef4444;
    }

    .status-dot.active {
      background: #22c55e;
      animation: pulse 2s infinite;
    }

    @keyframes pulse {
      0%, 100% { opacity: 1; }
      50% { opacity: 0.5; }
    }

    .status-text {
      font-size: 14px;
      font-weight: 500;
    }

    .stats-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;
      font-size: 12px;
    }

    .stat-item {
      display: flex;
      justify-content: space-between;
      padding: 4px 0;
    }

    .stat-label {
      color: #888888;
    }

    .stat-value {
      font-weight: 500;
      color: #ffffff;
    }

    .controls {
      display: flex;
      flex-direction: column;
      gap: 12px;
    }

    .btn {
      background: #333333;
      color: white;
      border: 1px solid #555555;
      border-radius: 6px;
      padding: 12px 16px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s;
    }

    .btn:hover {
      background: #444444;
      transform: translateY(-1px);
    }

    .btn:disabled {
      background: #222222;
      color: #666666;
      cursor: not-allowed;
      transform: none;
    }

    .btn-secondary {
      background: #222222;
      border-color: #444444;
    }

    .btn-secondary:hover {
      background: #333333;
    }

    .btn-danger {
      background: #dc2626;
      border-color: #dc2626;
    }

    .btn-danger:hover {
      background: #b91c1c;
    }

    .settings-section {
      margin-top: 20px;
      padding-top: 20px;
      border-top: 1px solid #333333;
    }

    .settings-title {
      font-size: 14px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #ffffff;
    }

    .setting-item {
      margin-bottom: 16px;
    }

    .setting-display-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 8px 0;
      border-bottom: 1px solid #333333;
    }

    .setting-display-item:last-child {
      border-bottom: none;
    }

    .setting-label {
      font-size: 12px;
      color: #888888;
    }

    .setting-value {
      font-size: 12px;
      color: #ffffff;
      font-weight: 500;
    }

    .input-group {
      margin-bottom: 12px;
    }

    .input-label {
      display: block;
      font-size: 12px;
      color: #888888;
      margin-bottom: 4px;
    }

    .input-field {
      width: 100%;
      padding: 8px 12px;
      border: 1px solid #444444;
      border-radius: 4px;
      font-size: 13px;
      background: #222222;
      color: #ffffff;
    }

    .input-field:focus {
      outline: none;
      border-color: #666666;
      box-shadow: 0 0 0 3px rgba(102, 102, 102, 0.1);
    }

    .error-message {
      color: #ef4444;
      font-size: 12px;
      margin-top: 4px;
      text-align: center;
    }

    .success-message {
      color: #22c55e;
      font-size: 12px;
      margin-top: 4px;
      text-align: center;
    }

    .loading {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 8px;
      color: #888888;
      font-size: 14px;
    }

    .spinner {
      width: 16px;
      height: 16px;
      border: 2px solid #333333;
      border-top: 2px solid #ffffff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% { transform: rotate(0deg); }
      100% { transform: rotate(360deg); }
    }
  </style>
  <script type="module" crossorigin src="/assets/index.html-DzSKpKHg.js"></script>
  <link rel="modulepreload" crossorigin href="/assets/browser-polyfill-DQnhcI3D.js">
</head>
<body>
  <div class="header">
    <h1>AIChromaPRO</h1>
    <p>Instagram Automation</p>
  </div>

  <div class="content">
    <div id="loading-screen" class="loading">
      <div class="spinner"></div>
      <span>Loading...</span>
    </div>

    <div id="main-content" style="display: none;">
      <!-- Status Card -->
      <div class="status-card">
        <div class="status-indicator">
          <div id="status-dot" class="status-dot"></div>
          <span id="status-text" class="status-text">Stopped</span>
        </div>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">Today's Actions:</span>
            <span id="today-actions" class="stat-value">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Pending Messages:</span>
            <span id="pending-messages" class="stat-value">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Collected Followers:</span>
            <span id="collected-followers" class="stat-value">0</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Daily Limit:</span>
            <span id="daily-limit" class="stat-value">50</span>
          </div>
        </div>
      </div>

      <!-- Initial Scraping Progress -->
      <div id="scraping-progress-card" class="status-card" style="display: none;">
        <div class="status-indicator">
          <div class="spinner" style="width: 12px; height: 12px;"></div>
          <span class="status-text">Collecting Followers...</span>
        </div>
        <div class="scraping-stats">
          <div class="stat-item" style="grid-column: span 2;">
            <span class="stat-label">Progress:</span>
            <span id="scraping-progress" class="stat-value">0 / 5,000</span>
          </div>
          <div class="progress-bar" style="margin-top: 12px; height: 4px; background: #333; border-radius: 2px; overflow: hidden;">
            <div id="progress-fill" style="height: 100%; background: #22c55e; width: 0%; transition: width 0.3s ease;"></div>
          </div>
        </div>
      </div>

      <!-- Controls -->
      <div class="controls">
        <button id="start-btn" class="btn">Start Extension</button>
        <button id="stop-btn" class="btn btn-danger" style="display: none;">Stop Extension</button>
        <button id="open-dashboard" class="btn btn-secondary">Open Dashboard</button>
      </div>

      <!-- Settings -->
      <div class="settings-section">
        <div class="settings-title">Settings</div>
        
        <div id="api-key-section" class="input-group">
          <label class="input-label" for="api-key">API Key</label>
          <input type="password" id="api-key" class="input-field" placeholder="Enter your API key">
          <button id="save-settings" class="btn" style="margin-top: 12px;">Save Settings</button>
        </div>

        <div id="connected-section" style="display: none;">
          <div class="connected-status">
            <div style="display: flex; align-items: center; gap: 8px; margin-bottom: 16px;">
              <div style="width: 8px; height: 8px; background: #22c55e; border-radius: 50%;"></div>
              <span style="font-size: 14px; font-weight: 500;">Connected</span>
            </div>
          </div>

          <!-- Extension Settings Display -->
          <div class="settings-display">
            <div class="setting-display-item">
              <span class="setting-label">Max Actions per Day:</span>
              <span id="display-max-actions" class="setting-value">50</span>
            </div>

            <div class="setting-display-item">
              <span class="setting-label">DMs Before Break:</span>
              <span id="display-dm-range" class="setting-value">7-10</span>
            </div>

            <div class="setting-display-item">
              <span class="setting-label">Break Time:</span>
              <span id="display-break-range" class="setting-value">30-60 min</span>
            </div>

            <div class="setting-display-item">
              <span class="setting-label">Delay Between DMs:</span>
              <span id="display-delay-between-dms" class="setting-value">5-15 min</span>
            </div>

            <div class="setting-display-item">
              <span class="setting-label">Natural Stop Time:</span>
              <span id="display-stop-time" class="setting-value">09:00-21:00</span>
            </div>

            <div style="margin-top: 12px; font-size: 12px; color: #888888; text-align: center;">
              Edit settings in dashboard
            </div>
          </div>
        </div>
      </div>

      <div id="error-message" class="error-message" style="display: none;"></div>
      <div id="success-message" class="success-message" style="display: none;"></div>
    </div>
  </div>

</body>
</html>