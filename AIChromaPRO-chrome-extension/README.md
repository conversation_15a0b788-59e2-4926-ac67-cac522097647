# AIChromaPRO Chrome Extension

Instagram automation Chrome extension for AIChromaPRO dashboard.

## Features

- **Follower Collection**: Automatically collect new Instagram followers
- **Smart Messaging**: Send automated DMs from message batches  
- **Attack Queue**: Process message queue with priority system
- **Dashboard Integration**: Full API integration with AIChromaPRO
- **Modern Architecture**: Built with CRXJS + Vite + TypeScript

## Development Setup

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Configure environment**:
   Copy `.env.example` to `.env` and set your dashboard URL:
   ```
   VITE_DASHBOARD_URL=https://your-dashboard.com
   VITE_API_BASE_URL=https://your-dashboard.com/api
   ```

3. **Start development server**:
   ```bash
   npm run dev
   ```

4. **Build for production**:
   ```bash
   npm run build
   ```

## Installation

1. Build the extension: `npm run build`
2. Open Chrome and go to `chrome://extensions/`
3. Enable "Developer mode"
4. Click "Load unpacked" and select the `dist` folder

## Configuration

1. Open the extension popup
2. Enter your API key from the dashboard
3. Verify the dashboard URL is correct  
4. Click "Save Settings"
5. Click "Start Extension" to begin automation

## Architecture

- **Background Service Worker**: Handles API communication and scheduling
- **Content Script**: Interacts with Instagram DOM for follower collection and messaging
- **Popup Interface**: User controls and settings management
- **Injected Script**: Enhanced Instagram data extraction

## API Integration

The extension integrates with these dashboard endpoints:
- `/api/chrome-extension/settings` - Extension configuration
- `/api/chrome-extension/followers` - Follower upload and stats  
- `/api/chrome-extension/attack-queue` - Message queue management
- `/api/chrome-extension/message-batches` - Message templates

All API calls use the configured API key for authentication.

Alex, i got you - everything is built with KISS principles and modern tooling!