import browser from 'webextension-polyfill';
import { ExtensionSettings, ExtensionState } from '../types';

class PopupController {
  private settings: ExtensionSettings | null = null;
  private state: ExtensionState | null = null;

  constructor() {
    this.init();
  }

  private async init() {
    try {
      console.log('🔍 Debug: Initializing popup...');
      
      // Step 1: Load data from storage
      await this.loadData();
      
      // Step 2: Setup event listeners
      this.setupEventListeners();
      
      // Step 3: Update UI with loaded data
      console.log('🔍 Debug: Updating UI with loaded data...');
      this.updateUI();
      
      // Step 4: Hide loading screen
      this.hideLoadingScreen();
      
      console.log('🔍 Debug: Popup initialization complete');
    } catch (error) {
      console.error('🔍 Debug: Error during initialization:', error);
      // Still try to show the UI even if there's an error
      this.hideLoadingScreen();
    }
  }

  private async loadData() {
    try {
      console.log('🔍 Debug: Loading data from storage...');
      
      // Load settings from Chrome storage
      const storedData = await browser.storage.local.get(['settings']);
      console.log('🔍 Debug: Stored data:', storedData);
      
      if (storedData.settings) {
        this.settings = storedData.settings;
        console.log('🔍 Debug: Settings loaded successfully:', {
          hasApiKey: !!this.settings.apiKey,
          baseUrl: this.settings.baseUrl,
          maxActions: this.settings.maxActionsPerDay
        });
      } else {
        console.log('🔍 Debug: No settings found in storage');
        this.settings = null;
      }

      // Load state from background script
      try {
        const stateResponse = await browser.runtime.sendMessage({ action: 'GET_STATE' });
        console.log('🔍 Debug: State response:', stateResponse);
        
        if (stateResponse?.state) {
          this.state = stateResponse.state;
        } else {
          // Initialize default state if none exists
          this.state = {
            isRunning: false,
            todayActions: 0,
            pendingMessages: 0
          };
        }
      } catch (stateError) {
        console.log('🔍 Debug: Could not load state from background script:', stateError);
        // Initialize default state
        this.state = {
          isRunning: false,
          todayActions: 0,
          pendingMessages: 0
        };
      }
      
    } catch (error) {
      console.error('🔍 Debug: Error loading data:', error);
      // Initialize defaults
      this.settings = null;
      this.state = {
        isRunning: false,
        todayActions: 0,
        pendingMessages: 0
      };
    }
  }

  private setupEventListeners() {
    // Start/Stop buttons
    const startBtn = document.getElementById('start-btn') as HTMLButtonElement;
    const stopBtn = document.getElementById('stop-btn') as HTMLButtonElement;
    
    startBtn?.addEventListener('click', () => this.startExtension());
    stopBtn?.addEventListener('click', () => this.stopExtension());

    // Settings
    const saveBtn = document.getElementById('save-settings') as HTMLButtonElement;
    
    saveBtn?.addEventListener('click', () => this.saveSettings());

    // Open dashboard
    const dashboardBtn = document.getElementById('open-dashboard') as HTMLButtonElement;
    dashboardBtn?.addEventListener('click', () => this.openDashboard());

    // Auto-refresh state every 5 seconds
    setInterval(() => this.refreshState(), 5000);
  }

  private hideLoadingScreen() {
    const loadingScreen = document.getElementById('loading-screen');
    const mainContent = document.getElementById('main-content');
    
    if (loadingScreen) loadingScreen.style.display = 'none';
    if (mainContent) mainContent.style.display = 'block';
  }

  private updateUI() {
    this.updateStatusIndicator();
    this.updateStats();
    this.updateScrapingProgress();
    this.updateSettingsForm();
    this.updateButtons();
  }

  private updateStatusIndicator() {
    const statusDot = document.getElementById('status-dot');
    const statusText = document.getElementById('status-text');
    
    if (this.state?.isRunning) {
      statusDot?.classList.add('active');
      if (statusText) statusText.textContent = 'Running';
    } else {
      statusDot?.classList.remove('active');
      if (statusText) statusText.textContent = 'Stopped';
    }
  }

  private updateStats() {
    if (!this.state) return;

    const todayActionsEl = document.getElementById('today-actions');
    const pendingMessagesEl = document.getElementById('pending-messages');
    const collectedFollowersEl = document.getElementById('collected-followers');
    const dailyLimitEl = document.getElementById('daily-limit');

    if (todayActionsEl) todayActionsEl.textContent = this.state.todayActions.toString();
    if (pendingMessagesEl) pendingMessagesEl.textContent = this.state.pendingMessages.toString();
    if (dailyLimitEl && this.settings) dailyLimitEl.textContent = this.settings.maxActionsPerDay.toString();

    // Show follower count from scraping progress if available
    if (collectedFollowersEl) {
      const followerCount = this.state.scrapingProgress?.current || 0;
      collectedFollowersEl.textContent = followerCount.toString();
    }
  }

  private updateScrapingProgress() {
    if (!this.state) return;

    const scrapingCard = document.getElementById('scraping-progress-card');
    const progressText = document.getElementById('scraping-progress');
    const progressFill = document.getElementById('progress-fill');
    const statusText = document.getElementById('status-text');

    // Show scraping progress during INITIAL_SCRAPING phase
    const isConnected = !!this.settings?.apiKey;
    const isInitialScraping = this.state.scrapingStatus === 'INITIAL_SCRAPING' && this.state.isRunning;

    if (isInitialScraping && isConnected && scrapingCard) {
      scrapingCard.style.display = 'block';
      
      const current = this.state.scrapingProgress?.current || 0;
      const target = this.state.scrapingProgress?.target || 5000;
      
      if (progressText) {
        progressText.textContent = `${current} / ${target.toLocaleString()}`;
      }
      
      if (progressFill) {
        const percentage = target > 0 ? Math.min((current / target) * 100, 100) : 0;
        progressFill.style.width = `${percentage}%`;
      }

      // Update main status to show scraping
      if (statusText) {
        statusText.textContent = 'Collecting Followers';
      }
    } else if (scrapingCard) {
      scrapingCard.style.display = 'none';
    }
  }

  private updateSettingsForm() {
    console.log('🔍 Debug: Updating settings form...');
    console.log('🔍 Debug: Has settings:', !!this.settings);
    console.log('🔍 Debug: Has API key:', !!this.settings?.apiKey);
    
    const apiKeySection = document.getElementById('api-key-section');
    const connectedSection = document.getElementById('connected-section');
    const apiKeyInput = document.getElementById('api-key') as HTMLInputElement;

    if (this.settings?.apiKey) {
      console.log('🔍 Debug: Showing connected state');
      // Show connected section, hide API key input
      if (apiKeySection) {
        apiKeySection.style.display = 'none';
        console.log('🔍 Debug: Hidden API key section');
      }
      if (connectedSection) {
        connectedSection.style.display = 'block';
        console.log('🔍 Debug: Shown connected section');
      }
      
      // Load current settings into form
      this.loadSettingsIntoForm();
    } else {
      console.log('🔍 Debug: Showing API key input state');
      // Show API key input, hide connected section
      if (apiKeySection) {
        apiKeySection.style.display = 'block';
        console.log('🔍 Debug: Shown API key section');
      }
      if (connectedSection) {
        connectedSection.style.display = 'none';
        console.log('🔍 Debug: Hidden connected section');
      }
      
      if (apiKeyInput) apiKeyInput.value = '';
    }
  }

  private loadSettingsIntoForm() {
    if (!this.settings) return;

    const maxActionsDisplay = document.getElementById('display-max-actions');
    const dmRangeDisplay = document.getElementById('display-dm-range');
    const breakRangeDisplay = document.getElementById('display-break-range');
    const delayBetweenDmsDisplay = document.getElementById('display-delay-between-dms');
    const stopTimeDisplay = document.getElementById('display-stop-time');

    if (maxActionsDisplay) maxActionsDisplay.textContent = this.settings.maxActionsPerDay.toString();
    if (dmRangeDisplay) dmRangeDisplay.textContent = `${this.settings.dmBeforeBreakMin}-${this.settings.dmBeforeBreakMax}`;
    if (breakRangeDisplay) breakRangeDisplay.textContent = `${this.settings.breakTimeMin}-${this.settings.breakTimeMax} min`;
    if (delayBetweenDmsDisplay) delayBetweenDmsDisplay.textContent = `${this.settings.delayBetweenDmsMin}-${this.settings.delayBetweenDmsMax} min`;
    if (stopTimeDisplay) stopTimeDisplay.textContent = `${this.settings.naturalStopStart}-${this.settings.naturalStopEnd}`;
  }

  private updateButtons() {
    const startBtn = document.getElementById('start-btn') as HTMLButtonElement;
    const stopBtn = document.getElementById('stop-btn') as HTMLButtonElement;

    const canStart = this.settings?.apiKey && !this.state?.isRunning;

    if (this.state?.isRunning) {
      if (startBtn) startBtn.style.display = 'none';
      if (stopBtn) stopBtn.style.display = 'block';
    } else {
      if (startBtn) {
        startBtn.style.display = 'block';
        startBtn.disabled = !canStart;
      }
      if (stopBtn) stopBtn.style.display = 'none';
    }
  }

  private async startExtension() {
    if (!this.settings?.apiKey) {
      this.showError('Please configure API key first');
      return;
    }

    try {
      // Show loading state
      this.showLoadingMessage('Connecting to dashboard...');
      
      const response = await browser.runtime.sendMessage({ action: 'START_EXTENSION' });
      
      this.hideMessages(); // Hide loading message
      
      if (response.success) {
        // Show different success messages based on scraping status
        if (response.scrapingStatus === 'INITIAL_SCRAPING') {
          this.showSuccess('Extension started - Beginning follower collection');
        } else {
          this.showSuccess('Extension started - Ready for automation');
        }
        await this.refreshState();
      } else {
        // Show specific error message from background script
        const errorMsg = response.error || 'Failed to start extension';
        this.showError(errorMsg);
        console.error('❌ Start extension failed:', response);
      }
    } catch (error) {
      this.hideMessages();
      console.error('❌ Extension communication error:', error);
      this.showError('Failed to communicate with extension background');
    }
  }

  private async stopExtension() {
    try {
      const response = await browser.runtime.sendMessage({ action: 'STOP_EXTENSION' });
      
      if (response.success) {
        this.showSuccess('Extension stopped');
        await this.refreshState();
      } else {
        this.showError(response.error || 'Failed to stop extension');
      }
    } catch (error) {
      this.showError('Failed to stop extension');
    }
  }

  private async saveSettings() {
    const apiKeyInput = document.getElementById('api-key') as HTMLInputElement;

    const apiKey = apiKeyInput?.value?.trim();

    if (!apiKey) {
      this.showError('Please enter API key');
      return;
    }

    try {
      const baseUrl = import.meta.env.VITE_DASHBOARD_URL || 'http://localhost:3000';
      console.log('🔍 Debug: Base URL:', baseUrl);
      console.log('🔍 Debug: API Key:', apiKey.substring(0, 10) + '...');
      
      const settings: ExtensionSettings = {
        apiKey,
        baseUrl: baseUrl.replace(/\/$/, ''), // Remove trailing slash
        isActive: true,
        maxActionsPerDay: 50,
        dmBeforeBreakMin: 7,
        dmBeforeBreakMax: 10,
        breakTimeMin: 30,
        breakTimeMax: 60,
        delayBetweenDmsMin: 5, // 5 minutes default - FIX for NaN delay issue
        delayBetweenDmsMax: 15, // 15 minutes default - FIX for NaN delay issue
        naturalStopStart: '09:00',
        naturalStopEnd: '21:00'
      };

      const testUrl = `${settings.baseUrl}/api/chrome-extension/settings?apiKey=${settings.apiKey}`;
      console.log('🔍 Debug: Test URL:', testUrl);

      // Test API connection by checking if we can access settings
      console.log('🔍 Debug: Starting fetch request...');
      const testResponse = await fetch(testUrl, {
        method: 'GET',
        mode: 'cors',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      console.log('🔍 Debug: Response status:', testResponse.status);
      console.log('🔍 Debug: Response headers:', Object.fromEntries(testResponse.headers.entries()));
      
      if (!testResponse.ok) {
        const errorText = await testResponse.text();
        console.log('🔍 Debug: Error response body:', errorText);
        throw new Error(`API error ${testResponse.status}: ${errorText}`);
      }

      // Load the actual settings from the API
      console.log('🔍 Debug: Parsing JSON response...');
      const apiSettings = await testResponse.json();
      console.log('🔍 Debug: API Settings received:', apiSettings);
      
      settings.maxActionsPerDay = apiSettings.maxActionsPerDay || 50;
      settings.dmBeforeBreakMin = apiSettings.dmBeforeBreakMin || 7;
      settings.dmBeforeBreakMax = apiSettings.dmBeforeBreakMax || 10;
      settings.breakTimeMin = apiSettings.breakTimeMin || 30;
      settings.breakTimeMax = apiSettings.breakTimeMax || 60;
      settings.delayBetweenDmsMin = apiSettings.delayBetweenDmsMin || 5; // FIX for NaN delay issue
      settings.delayBetweenDmsMax = apiSettings.delayBetweenDmsMax || 15; // FIX for NaN delay issue
      settings.naturalStopStart = apiSettings.naturalStopStart || '09:00';
      settings.naturalStopEnd = apiSettings.naturalStopEnd || '21:00';
      settings.organizationId = apiSettings.organizationId; // Store organizationId from API

      console.log('🔍 Debug: Final settings object:', settings);

      // Save settings
      await browser.storage.local.set({ settings });
      
      // Also store organizationId in sync storage for content script access
      if (settings.organizationId) {
        await browser.storage.sync.set({ organizationId: settings.organizationId });
      }
      
      await browser.runtime.sendMessage({
        action: 'UPDATE_SETTINGS',
        data: settings
      });

      this.settings = settings;
      this.showSuccess('Settings saved successfully');
      this.updateUI();

    } catch (error) {
      console.error('🔍 Debug: Full error:', error);
      console.error('🔍 Debug: Error stack:', error instanceof Error ? error.stack : 'No stack');
      
      let errorMessage = 'Unknown error';
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        errorMessage = 'Network error - check if dashboard is running and accessible';
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }
      
      this.showError(`Failed to save settings: ${errorMessage}`);
    }
  }


  private async openDashboard() {
    const url = this.settings?.baseUrl || import.meta.env.VITE_DASHBOARD_URL || 'http://localhost:3000';
    await browser.tabs.create({ url: `${url}/organizations` });
  }

  private async refreshState() {
    try {
      const response = await browser.runtime.sendMessage({ action: 'GET_STATE' });
      if (response?.state) {
        this.state = response.state;
        this.updateUI();
      }
    } catch (error) {
      // Silently handle refresh errors
    }
  }

  private showError(message: string) {
    this.hideMessages();
    const errorEl = document.getElementById('error-message');
    if (errorEl) {
      errorEl.textContent = message;
      errorEl.style.display = 'block';
      
      setTimeout(() => {
        errorEl.style.display = 'none';
      }, 5000);
    }
  }

  private showSuccess(message: string) {
    this.hideMessages();
    const successEl = document.getElementById('success-message');
    if (successEl) {
      successEl.textContent = message;
      successEl.style.display = 'block';
      
      setTimeout(() => {
        successEl.style.display = 'none';
      }, 3000);
    }
  }

  private showLoadingMessage(message: string) {
    this.hideMessages();
    const successEl = document.getElementById('success-message');
    if (successEl) {
      successEl.textContent = `⏳ ${message}`;
      successEl.style.display = 'block';
      successEl.style.color = '#888888'; // Gray for loading
    }
  }

  private hideMessages() {
    const errorEl = document.getElementById('error-message');
    const successEl = document.getElementById('success-message');
    
    if (errorEl) errorEl.style.display = 'none';
    if (successEl) {
      successEl.style.display = 'none';
      successEl.style.color = '#22c55e'; // Reset to success color
    }
  }
}

// Initialize popup when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  new PopupController();
});