import browser from 'webextension-polyfill';
import { FollowerData, AttackQueueItem } from '../types';
import { InstagramMessageSender } from './messageSender';
import { InstagramProfileNavigator } from './profileNavigator';
import { InstagramAPIFollowerScraper } from './apiFollowerScraper';
import { InstagramActivityMonitor } from './activityMonitor';

class InstagramContentScript {
  private messageSender: InstagramMessageSender;
  private profileNavigator: InstagramProfileNavigator;
  private apiFollowerScraper: InstagramAPIFollowerScraper;
  private activityMonitor: InstagramActivityMonitor;

  constructor() {
    this.messageSender = new InstagramMessageSender();
    this.profileNavigator = new InstagramProfileNavigator();
    this.apiFollowerScraper = new InstagramAPIFollowerScraper();
    this.activityMonitor = new InstagramActivityMonitor();
    this.init();
  }

  private init() {
    // Initialize injected script for message sending
    this.messageSender.initializeInjectedScript();
    
    // Listen for messages from background script (using native Chrome API like old extension)
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
      this.handleMessage(request, sender, sendResponse);
      return true; // Always keep message channel open for async operations
    });
    
    console.log('AIChromaPRO Instagram Content Script loaded');
  }

  private handleMessage(request: any, sender: any, sendResponse: (response?: any) => void) {
    console.log('📨 Content script received:', request.action);
    
    switch (request.action) {
      case 'PING':
        // Simple ping to check if content script is loaded
        sendResponse({ success: true });
        break;
      
      case 'SCRAPE_FOLLOWERS':
        // Use new API-based follower scraper (no modal needed!)
        console.log('📨 Content script received SCRAPE_FOLLOWERS');
        this.apiFollowerScraper.scrapeFollowersAPI(request).then(sendResponse);
        return true; // Keep message channel open
      
      case 'CHECK_ZERO_MESSAGES':
        console.log(`📨 [${new Date().toLocaleTimeString()}] CHECK_ZERO_MESSAGES request received for @${request.username}`);
        this.messageSender.getHaveZeroMessages(request.username).then(result => {
          sendResponse({ success: true, result });
        }).catch(error => {
          sendResponse({ success: false, error: error instanceof Error ? error.message : 'Unknown error' });
        });
        return true;

      case 'CHECK_NEW_FOLLOWERS':
        console.log(`📨 [${new Date().toLocaleTimeString()}] CHECK_NEW_FOLLOWERS request received`);
        this.activityMonitor.checkNewFollowers().then(sendResponse);
        return true;

      case 'SEND_MESSAGE':
        this.messageSender.sendMessage(request).then(sendResponse);
        return true;

      case 'VISIT_USER_MESSAGES':
        this.profileNavigator.visitUserMessages(request.username).then(sendResponse);
        return true;
      
      case 'VERIFY_MESSAGE_IN_DOM':
        console.log(`📨 [${new Date().toLocaleTimeString()}] VERIFY_MESSAGE_IN_DOM request received for @${request.username}`);
        this.messageSender.verifyMessageInDOM(request.username, request.expectedMessage).then(result => {
          sendResponse({ success: true, messageFound: result });
        }).catch(error => {
          sendResponse({ success: false, messageFound: false, error: error instanceof Error ? error.message : 'Unknown error' });
        });
        return true;
      
      default:
        sendResponse({ error: 'Unknown action' });
    }
  }
}

// Initialize content script
new InstagramContentScript();