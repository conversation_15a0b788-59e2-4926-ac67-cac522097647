// Instagram Follower Scraper - Clean implementation
import browser from 'webextension-polyfill';
import { FollowerData } from '../types';

export class InstagramFollowerScraper {
  private collectedFollowers: FollowerData[] = [];
  private isCollectingFollowers = false;
  private isInitialScraping = false;
  private scrapingTarget = 5000;
  private lastBatchUpload = 0;
  private scrapingStartTime: number = 0;
  private globalFollowerIndex: number = 0;

  // Open followers modal 
  async openFollowersModal(request: any): Promise<{ success: boolean; error?: string }> {
    const { username } = request;
    try {
      console.log(`🔄 Opening followers modal for @${username}...`);
      console.log('🔍 Current URL:', window.location.href);
      console.log('🔍 Current pathname:', window.location.pathname);
      
      // Check if we're on the correct profile page (handle both /username and /username/)
      const pathname = window.location.pathname;
      const isOnProfilePage = pathname === `/${username}` || pathname === `/${username}/` || pathname.startsWith(`/${username}/`);
      
      if (!isOnProfilePage) {
        const error = `Not on profile page for @${username}. Current URL: ${window.location.href}`;
        console.error('❌', error);
        return {
          success: false,
          error
        };
      }
      console.log('✅ On correct profile page');
      
      // Find and click followers button
      console.log('🔍 Looking for followers button...');
      const followersButton = await this.findFollowersLink(username);
      if (!followersButton) {
        console.error('❌ Could not find followers button');
        
        // Debug: Log all available links
        const allLinks = document.querySelectorAll('a');
        console.log('🔍 All links on page:', Array.from(allLinks).map(link => ({
          href: link.href,
          text: link.textContent?.trim(),
          innerHTML: link.innerHTML
        })).slice(0, 20)); // First 20 links
        
        return {
          success: false,
          error: 'Could not find followers button on profile page'
        };
      }
      
      console.log('✅ Found followers button:', followersButton);
      console.log('🔍 Button href:', followersButton.getAttribute('href'));
      console.log('🔍 Button text:', followersButton.textContent?.trim());
      
      console.log('📱 Clicking followers button...');
      followersButton.click();
      
      // Wait for modal to open
      console.log('⏳ Waiting 2s for modal to open...');
      await this.delay(2000);
      
      // Check if modal opened
      console.log('🔍 Checking if modal opened...');
      const modal = document.querySelector('[role="dialog"]');
      if (!modal) {
        console.error('❌ Followers modal did not open');
        
        // Debug: Check what happened after click
        console.log('🔍 Current URL after click:', window.location.href);
        console.log('🔍 Any dialogs found:', document.querySelectorAll('[role="dialog"]').length);
        console.log('🔍 Any modals found:', document.querySelectorAll('.modal, [class*="modal"]').length);
        
        return {
          success: false,
          error: 'Followers modal did not open'
        };
      }
      
      console.log('✅ Followers modal opened successfully');
      return { success: true };
      
    } catch (error) {
      console.error('❌ Error opening followers modal:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  // Scrape followers from modal
  async scrapeFollowersFromModal(request: any): Promise<{ success: boolean; followers?: FollowerData[]; error?: string }> {
    const { count, username } = request;
    try {
      console.log(`🔄 Starting to scrape ${count} followers for @${username}...`);
      
      if (!username) {
        throw new Error('Username is required');
      }
      
      // Check if modal is already open
      const modal = document.querySelector('[role="dialog"]');
      if (!modal) {
        throw new Error('Followers modal is not open');
      }
      
      // Use old extension scraping approach
      const followers = await this.scrapeFollowersFromModalOld(count);
      
      // Close modal
      this.closeModal();
      
      console.log(`✅ Scraped ${followers.length} followers`);
      return { success: true, followers };
      
    } catch (error) {
      console.error('❌ Scraping error:', error);
      return { success: false, error: error.message };
    }
  }

  // Old extension scraping logic (proven to work)
  private async scrapeFollowersFromModalOld(targetCount: number): Promise<FollowerData[]> {
    // Capture the start timestamp for this scraping session
    const scrapingStartTime = Date.now();
    console.log(`📅 Scraping session started at: ${new Date(scrapingStartTime).toISOString()}`);
    
    const followers = new Set<string>();
    let scrollContainer: Element | null = null;
    let lastHeight = 0;
    let noNewFollowersCount = 0;
    let followerIndex = 0; // Track the order of followers collected
    
    // Find scroll container - look for element with overflow-y: scroll
    const modal = document.querySelector('[role="dialog"]');
    if (!modal) {
      console.error('Modal not found');
      return [];
    }
    
    console.log('🔍 Finding scroll container...');
    
    // First try specific selectors (from old extension)
    const containerSelectors = [
      'div._aano', // Common Instagram modal class
      'div[style*="overflow-y: scroll"]',
      'div[style*="overflow: scroll"]',
      'div[class*="scroll"]',
    ];
    
    for (const selector of containerSelectors) {
      const element = modal.querySelector(selector);
      if (element && element.scrollHeight > element.clientHeight) {
        scrollContainer = element;
        console.log(`✅ Found scroll container with selector: ${selector}`);
        break;
      }
    }
    
    // If not found, check all divs in modal for computed overflow-y: scroll
    if (!scrollContainer) {
      console.log('🔍 Checking all divs for overflow-y: scroll...');
      const allDivs = modal.querySelectorAll('div');
      for (const div of allDivs) {
        const computedStyle = window.getComputedStyle(div);
        if (computedStyle.overflowY === 'scroll' || computedStyle.overflow === 'scroll') {
          if (div.scrollHeight > div.clientHeight) {
            scrollContainer = div;
            console.log('✅ Found scroll container by computed style');
            break;
          }
        }
      }
    }
    
    // Final fallback - find any scrollable element in modal
    if (!scrollContainer) {
      console.log('🔍 Using fallback - checking all elements for scrollability...');
      const allElements = modal.querySelectorAll('*');
      for (const element of allElements) {
        if (element.scrollHeight > element.clientHeight && element.scrollHeight > 100) {
          scrollContainer = element;
          console.log(`✅ Found scrollable element: ${element.tagName}.${element.className}`);
          break;
        }
      }
    }
    
    if (!scrollContainer) {
      console.error('❌ Could not find scroll container');
      return [];
    }
    
    console.log(`🔄 Starting to scroll and collect followers (target: ${targetCount})...`);
    
    // Scroll and collect followers (old extension logic + profile photos)
    const followerDataMap = new Map<string, FollowerData>(); // Use Map instead of Set for full data
    
    while (followerDataMap.size < targetCount) {
      // Get current followers in view
      const followerElements = scrollContainer.querySelectorAll('a[href^="/"][role="link"]');
      
      for (const element of followerElements) {
        const href = (element as HTMLAnchorElement).href;
        if (href && href.match(/instagram\.com\/([^\/]+)\/?$/)) {
          const username = href.split('/').filter(Boolean).pop();
          if (username && !username.includes('?') && username !== 'explore' && !followerDataMap.has(username)) {
            
            // Extract profile photo from img tag
            const imgElement = element.querySelector('img') as HTMLImageElement;
            const profilePhoto = imgElement?.src || undefined;
            
            // Extract display name/nickname if available
            const displayNameElement = element.querySelector('[dir="auto"]');
            const nickname = displayNameElement?.textContent?.trim() || username;
            
            // Use negative timestamps: first follower = start time, then -1 sec per follower
            const followerTimestamp = new Date(scrapingStartTime - (followerIndex * 1000));
            
            const followerData: FollowerData = {
              instagramUserId: username,
              nickname: nickname,
              profilePhoto: profilePhoto,
              collectedAt: followerTimestamp.toISOString()
            };
            
            followerDataMap.set(username, followerData);
            followerIndex++; // Increment for next follower
            
            console.log(`✅ Collected #${followerIndex}: ${username} (${nickname}) - Photo: ${profilePhoto ? 'Yes' : 'No'} - Time: ${followerTimestamp.toISOString()}`);
          }
        }
        
        if (followerDataMap.size >= targetCount) {
          break;
        }
      }
      
      console.log(`📊 Progress: ${followerDataMap.size}/${targetCount} followers collected`);
      
      // Check if we got new followers
      if (scrollContainer.scrollHeight === lastHeight) {
        noNewFollowersCount++;
        if (noNewFollowersCount > 3) {
          console.log('📝 No more followers to load');
          break;
        }
      } else {
        noNewFollowersCount = 0;
        lastHeight = scrollContainer.scrollHeight;
      }
      
      // Scroll down
      scrollContainer.scrollTop = scrollContainer.scrollHeight;
      await this.delay(1500); // Wait for new followers to load
    }
    
    // Convert Map to FollowerData array
    const followersArray: FollowerData[] = Array.from(followerDataMap.values());
    
    console.log(`🎯 Collected ${followersArray.length} followers total with profile photos`);
    
    // Log stats about profile photos
    const withPhotos = followersArray.filter(f => f.profilePhoto).length;
    const withoutPhotos = followersArray.length - withPhotos;
    console.log(`📸 Profile photos: ${withPhotos} with photos, ${withoutPhotos} without photos`);
    
    return followersArray;
  }

  private async findFollowersLink(username: string): Promise<HTMLElement | null> {
    // Wait for profile header to load
    await this.waitForElement('header section', 10000);
    
    // Use the working selector
    console.log('🔍 Using selector: a[href*="/followers/"]');
    const element = document.querySelector('a[href*="/followers/"]') as HTMLElement;
    
    if (element) {
      console.log(`✅ Found followers link with working selector`);
      console.log('🔍 Link href:', element.getAttribute('href'));
      console.log('🔍 Link text:', element.textContent?.trim());
      return element;
    }

    console.log('❌ Working selector failed, trying fallbacks...');
    
    // Fallback: Look for any link containing "follower" text
    const allLinks = document.querySelectorAll('a');
    for (const link of allLinks) {
      const text = link.textContent?.toLowerCase() || '';
      const href = link.getAttribute('href') || '';
      
      if ((text.includes('follower') || href.includes('/followers/')) && 
          link.offsetParent !== null) { // Ensure it's visible
        console.log('✅ Found followers link by text content:', text);
        return link as HTMLElement;
      }
    }

    return null;
  }

  private closeModal(): void {
    console.log('🔄 Closing followers modal...');
    
    // Find close button by aria-label (both Polish and English)
    const closeSelectors = [
      'svg[aria-label="Zamknij"]',  // Polish
      'svg[aria-label="Close"]',    // English
      'svg[aria-label*="close"]',   // Case insensitive fallback
      'svg[aria-label*="Close"]',   // Alternative case
      'button[aria-label="Zamknij"]', // If it's directly on button
      'button[aria-label="Close"]'    // If it's directly on button
    ];
    
    for (const selector of closeSelectors) {
      const closeElement = document.querySelector(selector);
      if (closeElement) {
        console.log(`✅ Found close button with selector: ${selector}`);
        
        // Find the clickable parent (button or div)
        const clickable = closeElement.closest('button') || 
                         closeElement.closest('div[role="button"]') || 
                         closeElement.parentElement;
        
        if (clickable) {
          console.log('📱 Clicking close button...');
          (clickable as HTMLElement).click();
          
          // Wait a moment to verify modal closed
          setTimeout(() => {
            const modal = document.querySelector('[role="dialog"]');
            if (!modal) {
              console.log('✅ Modal closed successfully');
            } else {
              console.log('⚠️ Modal still visible after close attempt');
            }
          }, 1000);
          
          return;
        }
      }
    }
    
    console.log('❌ Close button not found, trying Escape key...');
    // Fallback: press Escape
    document.dispatchEvent(new KeyboardEvent('keydown', { key: 'Escape', keyCode: 27 }));
    
    // Check if Escape worked
    setTimeout(() => {
      const modal = document.querySelector('[role="dialog"]');
      if (!modal) {
        console.log('✅ Modal closed with Escape key');
      } else {
        console.log('⚠️ Modal still visible after Escape key');
      }
    }, 1000);
  }

  private async waitForElement(selector: string, timeout = 10000): Promise<Element | null> {
    return new Promise((resolve) => {
      const element = document.querySelector(selector);
      if (element) {
        resolve(element);
        return;
      }

      const observer = new MutationObserver(() => {
        const element = document.querySelector(selector);
        if (element) {
          observer.disconnect();
          resolve(element);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      setTimeout(() => {
        observer.disconnect();
        resolve(null);
      }, timeout);
    });
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}