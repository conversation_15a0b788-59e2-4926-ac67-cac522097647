// Instagram API-based Follower Scraper - Direct API calls (no DOM scraping)
import { FollowerData } from '../types';

export class InstagramAPIFollowerScraper {
  private readonly PAGE_COUNT = 24; // Default batch size
  private readonly MIN_DELAY_MS = 2000; // Increased from 700ms
  private readonly MAX_DELAY_MS = 5000; // Increased from 1300ms

  // Helper methods
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private getRandomDelay(currentFollowerCount: number = 0): number {
    // Progressive slowdown based on how many followers we've collected
    let minDelay = this.MIN_DELAY_MS;
    let maxDelay = this.MAX_DELAY_MS;
    
    if (currentFollowerCount >= 3000) {
      // Very slow for 3000+ followers
      minDelay = 4000;
      maxDelay = 8000;
    } else if (currentFollowerCount >= 1000) {
      // Slower for 1000-3000 followers
      minDelay = 3000;
      maxDelay = 6000;
    }
    
    return minDelay + Math.random() * (maxDelay - minDelay);
  }
  
  private getDynamicBatchSize(currentFollowerCount: number): number {
    // Reduce batch size for large follower counts
    if (currentFollowerCount >= 1000) {
      return 12; // Half the normal batch size
    }
    return this.PAGE_COUNT;
  }
  
  private shouldTakeLongBreak(currentFollowerCount: number): boolean {
    // Take a long break every 500 followers
    return currentFollowerCount > 0 && currentFollowerCount % 500 === 0;
  }

  private getHeaders() {
    return {
      'X-IG-App-ID': '936619743392459',
      'X-Requested-With': 'XMLHttpRequest',
      'Referer': location.href
    };
  }

  private async fetchJSON(url: string): Promise<any> {
    // First try direct fetch from content script
    try {
      const response = await fetch(url, { 
        credentials: 'include', 
        headers: this.getHeaders() 
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status} ${response.statusText}`);
      }
      
      const text = await response.text();
      
      // Check if we got HTML instead of JSON (auth/error page)
      if (text.startsWith('<!DOCTYPE') || text.includes('<html')) {
        console.log('⚠️ Got HTML response, trying injected script method...');
        // Fall back to injected script method
        return this.fetchViaInjectedScript(url);
      }
      
      return JSON.parse(text);
    } catch (error) {
      console.log('⚠️ Direct fetch failed, trying injected script method...', error);
      // Fall back to injected script method
      return this.fetchViaInjectedScript(url);
    }
  }

  private fetchViaInjectedScript(url: string): Promise<any> {
    return new Promise((resolve, reject) => {
      // Create unique ID for this request
      const requestId = `api_request_${Date.now()}_${Math.random()}`;
      
      // Listen for response
      const handleMessage = (event: MessageEvent) => {
        if (event.data.type === 'API_RESPONSE' && event.data.requestId === requestId) {
          window.removeEventListener('message', handleMessage);
          
          if (event.data.success) {
            resolve(event.data.data);
          } else {
            reject(new Error(event.data.error));
          }
        }
      };
      
      window.addEventListener('message', handleMessage);
      
      // Inject script to make the request in page context
      const script = document.createElement('script');
      script.textContent = `
        (async () => {
          try {
            const response = await fetch('${url}', {
              credentials: 'include',
              headers: {
                'X-IG-App-ID': '936619743392459',
                'X-Requested-With': 'XMLHttpRequest',
                'Referer': location.href
              }
            });
            
            const text = await response.text();
            
            if (text.startsWith('<!DOCTYPE') || text.includes('<html')) {
              throw new Error('Got HTML response instead of JSON - authentication may be required');
            }
            
            const data = JSON.parse(text);
            
            window.postMessage({
              type: 'API_RESPONSE',
              requestId: '${requestId}',
              success: true,
              data: data
            }, '*');
          } catch (error) {
            window.postMessage({
              type: 'API_RESPONSE',
              requestId: '${requestId}',
              success: false,
              error: error.message
            }, '*');
          }
        })();
      `;
      
      document.head.appendChild(script);
      script.remove();
      
      // Timeout after 10 seconds
      setTimeout(() => {
        window.removeEventListener('message', handleMessage);
        reject(new Error('API request timeout'));
      }, 10000);
    });
  }

  // Get user ID from username using Instagram API
  async getUserIdByUsername(username: string): Promise<string> {
    const url = `https://www.instagram.com/api/v1/users/web_profile_info/?username=${encodeURIComponent(username)}`;
    const data = await this.fetchJSON(url);
    const userId = data?.data?.user?.id;
    
    if (!userId) {
      throw new Error(`Could not get user ID for username: ${username}`);
    }
    
    console.log(`✅ Got user ID for @${username}: ${userId}`);
    return userId;
  }

  // Main scraping method - fetches followers using Instagram API
  // Open followers modal to appear like a real user
  private async openFollowersModal(username: string): Promise<boolean> {
    try {
      console.log(`📱 Opening followers modal for @${username}...`);
      
      // Check if we're on the correct profile page
      const currentUrl = window.location.pathname;
      const expectedUrl = `/${username}/`;
      
      if (!currentUrl.includes(username)) {
        console.log(`📍 Not on ${username}'s profile. Current: ${currentUrl}`);
        // Navigate to profile first
        window.location.href = `https://www.instagram.com/${username}/`;
        // Wait for page load
        await this.sleep(3000);
        return false;
      }
      
      // Find and click the followers link
      const followersLink = document.querySelector('a[href*="/followers/"]') as HTMLElement;
      
      if (!followersLink) {
        console.log('❌ Followers link not found on page');
        // Try alternative selector - the followers count section
        const alternativeLink = Array.from(document.querySelectorAll('a')).find(
          el => el.textContent?.includes('follower')
        ) as HTMLElement;
        
        if (alternativeLink) {
          alternativeLink.click();
        } else {
          console.log('❌ No followers link found at all');
          return false;
        }
      } else {
        followersLink.click();
      }
      
      // Wait for modal to open
      console.log('⏳ Waiting for modal to open...');
      await this.sleep(2000);
      
      // Check if modal is open
      const modal = document.querySelector('div[role="dialog"]');
      if (modal) {
        console.log('✅ Followers modal opened successfully');
        return true;
      } else {
        console.log('⚠️ Modal not detected, but continuing anyway');
        return true; // Continue anyway, API might still work
      }
      
    } catch (error) {
      console.error('❌ Error opening followers modal:', error);
      return false;
    }
  }

  async scrapeFollowersAPI(request: any): Promise<{ success: boolean; followers?: FollowerData[]; error?: string; nextMaxId?: string }> {
    const { count, username, startMaxId } = request;
    
    try {
      console.log(`🚀 Starting hybrid follower scraping for @${username} (target: ${count})`);
      console.log('📍 Current page:', window.location.href);
      console.log('🍪 Cookies available:', document.cookie ? 'Yes' : 'No');
      
      // Step 1: Open followers modal to appear like a normal user
      const modalOpened = await this.openFollowersModal(username);
      if (!modalOpened) {
        console.log('⚠️ Modal opening failed, but continuing with API...');
      }
      
      // Step 2: Get user ID from username
      const userId = await this.getUserIdByUsername(username);
      
      // Step 3: Fetch followers using API while modal is open
      const { followers, nextMaxId } = await this.fetchFollowersByUserId(userId, count, startMaxId);
      
      // Step 4: Convert to our format
      const formattedFollowers = this.formatFollowersForDatabase(followers);
      
      // Step 5: Close modal if it's open
      const closeButton = document.querySelector('div[role="dialog"] button[aria-label="Close"]') as HTMLElement;
      if (closeButton) {
        closeButton.click();
        console.log('📱 Modal closed');
      }
      
      console.log(`✅ Hybrid scraping completed! Collected ${formattedFollowers.length} followers`);
      
      return {
        success: true,
        followers: formattedFollowers,
        nextMaxId: nextMaxId
      };
      
    } catch (error) {
      console.error('❌ Hybrid scraping failed:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  // Fetch followers with pagination
  private async fetchFollowersByUserId(userId: string, limit: number, startMaxId?: string): Promise<{ followers: any[]; nextMaxId?: string }> {
    let maxId: string | null = startMaxId || null;
    const followers: any[] = [];

    console.log(`📡 Starting follower fetch for user ID: ${userId} (limit: ${limit})`);

    while (followers.length < limit) {
      // Dynamic batch size based on current count
      const batchSize = this.getDynamicBatchSize(followers.length);
      
      const url = new URL(`https://www.instagram.com/api/v1/friendships/${userId}/followers/`);
      url.searchParams.set('count', String(batchSize));
      url.searchParams.set('search_surface', 'follow_list_page');
      
      if (maxId) {
        url.searchParams.set('max_id', maxId);
      }

      console.log(`📡 Fetching batch... (current: ${followers.length}/${limit}, batch size: ${batchSize})`);
      
      const data = await this.fetchJSON(url.toString());
      const users = Array.isArray(data?.users) ? data.users : [];
      
      if (users.length === 0) {
        console.log('📭 No more followers available');
        break;
      }
      
      followers.push(...users);
      console.log(`📊 Batch complete: +${users.length} followers (total: ${followers.length})`);

      // Send progress update to background script
      try {
        chrome.runtime.sendMessage({
          action: 'SCRAPING_PROGRESS_UPDATE',
          data: {
            current: followers.length,
            total: limit
          }
        });
      } catch (error) {
        console.log('Could not send progress update:', error);
      }

      // Check if we have more pages
      if (!data?.next_max_id) {
        console.log('📄 Reached last page');
        break;
      }
      
      maxId = data.next_max_id;

      // Check if we should take a long break
      if (this.shouldTakeLongBreak(followers.length)) {
        const longBreak = 10000 + Math.random() * 10000; // 10-20 seconds
        console.log(`☕ Taking a long break (${Math.round(longBreak/1000)}s) at ${followers.length} followers...`);
        await this.sleep(longBreak);
      } else {
        // Anti-detection delay between requests with progressive slowdown
        const delay = this.getRandomDelay(followers.length);
        console.log(`⏳ Waiting ${Math.round(delay)}ms before next batch...`);
        await this.sleep(delay);
      }
    }

    return { 
      followers: followers.slice(0, limit), // Return exact count requested
      nextMaxId: maxId // Return pagination state for next batch
    };
  }

  // Convert Instagram API response to our FollowerData format
  private formatFollowersForDatabase(apiFollowers: any[]): FollowerData[] {
    const startTime = Date.now();
    
    return apiFollowers.map((user, index) => {
      // First follower (index 0) gets current timestamp, subsequent followers get -1 minute intervals
      // This ensures chronological order: newest → oldest as we scroll down Instagram
      const followerTimestamp = new Date(startTime - (index * 60000)); // 1 minute intervals
      
      const followerData: FollowerData = {
        instagramUserId: user.pk || user.pk_id || user.id, // Real Instagram user ID
        nickname: user.username || 'unknown_user',
        profilePhoto: user.profile_pic_url || undefined,
        collectedAt: followerTimestamp.toISOString()
      };
      
      console.log(`✅ Formatted follower #${index + 1}: ${followerData.nickname} (ID: ${followerData.instagramUserId})`);
      return followerData;
    });
  }
}