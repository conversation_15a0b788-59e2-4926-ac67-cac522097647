// Instagram Profile Navigation - Clean implementation

export class InstagramProfileNavigator {
  
  // Visit user messages page (click Message button on profile)
  async visitUserMessages(username: string): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('visitUserMessages called', { username, currentUrl: window.location.href });
    
      // Wait for page to fully load (increased for stability)
      await this.delay(8000);
      
      let messageButton: Element | null = null;
      
      // Extended selectors for different button types (same as original)
      const buttonSelectors = [
        'div[role="button"]',
        'button',
        'a[role="button"]',
        'header button',
        'header div[role="button"]',
        'article button',
        'article div[role="button"]',
        '[data-testid*="message"]',
        '[data-testid*="send"]'
      ];
      
      const allButtons: Element[] = [];
      for (const selector of buttonSelectors) {
        const elements = document.querySelectorAll(selector);
        allButtons.push(...Array.from(elements));
      }
      
      // Remove duplicates
      const uniqueButtons = Array.from(new Set(allButtons));
      console.log(`Found ${uniqueButtons.length} potential buttons to check for "Message" text.`);

      // Log all button texts for debugging with more details (from original)
      const buttonTexts = uniqueButtons.map(btn => ({
        text: (btn as HTMLElement).innerText?.trim() || '',
        ariaLabel: btn.getAttribute('aria-label'),
        title: btn.getAttribute('title'),
        className: btn.className,
        tagName: btn.tagName,
        testId: btn.getAttribute('data-testid')
      })).filter(btn => btn.text.length > 0 || btn.ariaLabel || btn.testId);
      
      console.log('All button details found:', buttonTexts);

      // Try multiple text patterns for different languages (same as original)
      const messagePatterns = [
        'message',
        'wiadomość',
        'napisz',
        'send message',
        'wyślij wiadomość',
        'dm',
        'direct',
        'chat'
      ];

      for (const btn of uniqueButtons) {
        const buttonText = ((btn as HTMLElement).innerText || '').toLowerCase().trim();
        const ariaLabel = (btn.getAttribute('aria-label') || '').toLowerCase();
        const title = (btn.getAttribute('title') || '').toLowerCase();
        const testId = (btn.getAttribute('data-testid') || '').toLowerCase();
        
        // Check all text sources for message patterns
        const allTexts = [buttonText, ariaLabel, title, testId].join(' ');
        
        for (const pattern of messagePatterns) {
          if (allTexts.includes(pattern)) {
            messageButton = btn;
            console.log('Found message button by pattern:', {
              pattern,
              buttonText,
              ariaLabel,
              title,
              testId,
              element: btn
            });
            break;
          }
        }
        
        if (messageButton) break;
      }

      if (messageButton) {
        await this.delay(4000);
        console.log('visitUserMessages: Attempting to click message_button:', messageButton);
        
        // Try multiple click methods (EXACT copy from original)
        try {
          // Method 1: Regular click
          (messageButton as HTMLElement).click();
          await this.delay(5000);
          
          // Check if navigation happened
          if (window.location.href.includes('/direct/')) {
            console.log('✅ Successfully navigated to DM page via regular click');
          } else {
            // Method 2: Dispatch click event
            console.log('Regular click failed, trying event dispatch');
            const clickEvent = new MouseEvent('click', {
              bubbles: true,
              cancelable: true,
              view: window
            });
            messageButton.dispatchEvent(clickEvent);
            await this.delay(5000);
            
            if (window.location.href.includes('/direct/')) {
              console.log('✅ Successfully navigated to DM page via event dispatch');
            } else {
              // Method 3: Focus and Enter key (from original)
              console.log('Event dispatch failed, trying focus + Enter');
              (messageButton as HTMLElement).focus();
              await this.delay(2000);
              const enterEvent = new KeyboardEvent('keydown', {
                key: 'Enter',
                code: 'Enter',
                keyCode: 13,
                which: 13,
                bubbles: true
              });
              messageButton.dispatchEvent(enterEvent);
              await this.delay(5000);
              
              if (window.location.href.includes('/direct/')) {
                console.log('✅ Successfully navigated to DM page via Enter key');
              } else {
                console.log('❌ All click methods failed - DM page not opened');
              }
            }
          }
        } catch (error) {
          console.log('❌ Error clicking message button:', error);
        }
        
        console.log('visitUserMessages: After message button interaction', { finalUrl: window.location.href });
      } else {
        console.log('❌ visitUserMessages: Direct message button not found. Trying three-dots menu approach...');
        
        // Look for three-dots menu (Options) button
        let optionsButton: Element | null = null;
        
        // Look for options button by aria-label and SVG content (from original)
        for (const btn of uniqueButtons) {
          const ariaLabel = (btn.getAttribute('aria-label') || '').toLowerCase();
          const svgTitle = btn.querySelector('svg title')?.textContent?.toLowerCase() || '';
          
          if (ariaLabel.includes('opcje') || ariaLabel.includes('options') ||
              svgTitle.includes('opcje') || svgTitle.includes('options')) {
            optionsButton = btn;
            console.log('Found options button:', { ariaLabel, svgTitle, element: btn });
            break;
          }
        }
        
        if (optionsButton) {
          console.log('📱 Clicking options button to reveal message option...');
          try {
            (optionsButton as HTMLElement).click();
            await this.delay(5000); // Wait for menu to appear
            
            // Now look for the message button in the dropdown menu
            const newButtons = document.querySelectorAll('button, div[role="button"]');
            let messageButtonInMenu: Element | null = null;
            
            for (const btn of newButtons) {
              const buttonText = ((btn as HTMLElement).innerText || '').toLowerCase().trim();
              // Exact match for Polish "Wyślij wiadomość" (from original)
              if (buttonText === 'wyślij wiadomość' ||
                  buttonText.includes('send message') ||
                  buttonText.includes('message') ||
                  buttonText === 'wiadomość') {
                messageButtonInMenu = btn;
                console.log('Found message button in dropdown menu:', {
                  text: buttonText,
                  exactText: (btn as HTMLElement).innerText,
                  element: btn
                });
                break;
              }
            }
            
            if (messageButtonInMenu) {
              console.log('📱 Clicking message button from dropdown menu...');
              (messageButtonInMenu as HTMLElement).click();
              // Wait for navigation with retry logic (from original)
              let navigationSuccess = false;
              for (let attempt = 1; attempt <= 5; attempt++) {
                await this.delay(6000);
                
                if (window.location.href.includes('/direct/')) {
                  console.log('✅ Successfully navigated to DM page via dropdown menu');
                  navigationSuccess = true;
                  break;
                } else {
                  console.log(`❌ Attempt ${attempt}/5 - Dropdown menu click failed, URL: ${window.location.href}`);
                  
                  if (attempt < 5) {
                    // Try alternative approach (from original)
                    console.log('🔄 Retrying with direct message button approach...');
                    
                    const directMessageBtn = document.querySelector('div[aria-label*="Message"], div[aria-label*="Wiadomość"], a[aria-label*="Message"], a[aria-label*="Wiadomość"]');
                    if (directMessageBtn) {
                      console.log('Found direct message button, clicking...');
                      (directMessageBtn as HTMLElement).click();
                    } else {
                      // Try clicking the message button from dropdown again
                      const allButtons = document.querySelectorAll('button, div[role="button"]');
                      const retryMessageBtn = Array.from(allButtons).find(btn => {
                        const text = (btn as HTMLElement).textContent?.toLowerCase() || '';
                        return text.includes('message') || text.includes('wiadomość') || text.includes('wyślij wiadomość');
                      });
                      if (retryMessageBtn) {
                        console.log('🔄 Retrying with found message button:', (retryMessageBtn as HTMLElement).textContent);
                        (retryMessageBtn as HTMLElement).click();
                      }
                    }
                  }
                }
              }
              
              if (!navigationSuccess) {
                console.log('❌ Failed to navigate to DM page after 5 attempts via dropdown menu');
                return { success: false, error: 'Failed to navigate to DM page after 5 attempts via dropdown menu' };
              }
            } else {
              console.log('❌ Message button not found in dropdown menu');
              
              // Log what buttons are available in the menu (from original)
              const menuButtons = Array.from(newButtons).map(btn => ({
                text: ((btn as HTMLElement).innerText || '').trim(),
                ariaLabel: btn.getAttribute('aria-label'),
                className: btn.className
              })).filter(btn => btn.text.length > 0);
              
              console.log('Available buttons in dropdown menu:', menuButtons);
              return { success: false, error: 'Message button not found in dropdown menu' };
            }
          } catch (error) {
            console.log('❌ Error interacting with options menu:', error);
            return { success: false, error: `Error interacting with options menu: ${error}` };
          }
        } else {
          console.log('❌ Options button not found either. Available button details:', buttonTexts);
          return { success: false, error: 'Message button not found on profile' };
        }
      }
      
      console.log('visitUserMessages completed', { username, finalUrl: window.location.href });
      return { success: true };

    } catch (error) {
      console.error(`❌ Error visiting messages for @${username}:`, error);
      return { 
        success: false, 
        error: error.message 
      };
    }
  }

  // Navigate to user profile with retry mechanism
  async navigateToProfile(request: any): Promise<{ success: boolean; error?: string }> {
    const { username } = request;
    const maxRetries = 3;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`📍 Navigating to profile: @${username} (Attempt ${attempt}/${maxRetries})`);
        console.log(`📍 Current URL: ${window.location.href}`);
        
        const profileUrl = `https://instagram.com/${username}`;
        const currentUrl = window.location.href;
        
        if (!currentUrl.includes(`/${username}`)) {
          console.log(`📍 Navigation needed - going to: ${profileUrl}`);
          window.location.href = profileUrl;
          
          // Wait for navigation
          const navigationSuccess = await this.waitForNavigation(`/${username}`);
          if (navigationSuccess) {
            console.log(`✅ Navigation completed`);
            return { success: true };
          } else {
            throw new Error('Navigation timeout - profile page did not load');
          }
        } else {
          console.log(`✅ Already on target profile page`);
          return { success: true };
        }

      } catch (error) {
        console.error(`❌ Navigation attempt ${attempt} failed:`, error);
        
        if (attempt < maxRetries) {
          console.log(`🔄 Refreshing page and retrying in 5 seconds...`);
          window.location.reload();
          await this.delay(5000);
        } else {
          console.error('❌ All navigation attempts failed');
          return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
        }
      }
    }
    
    return { success: false, error: 'Max retries exceeded' };
  }

  private async waitForNavigation(expectedPath: string, timeout = 10000): Promise<boolean> {
    return new Promise((resolve) => {
      const checkPath = () => {
        if (window.location.pathname.includes(expectedPath)) {
          resolve(true);
          return;
        }
      };

      // Check immediately
      checkPath();

      // Set up interval to check periodically
      const interval = setInterval(checkPath, 100);

      setTimeout(() => {
        clearInterval(interval);
        resolve(false);
      }, timeout);
    });
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}