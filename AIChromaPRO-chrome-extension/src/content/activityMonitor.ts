// Instagram Activity Monitor - Clean implementation for new follower detection

export class InstagramActivityMonitor {
  
  // Check for new followers using Instagram Activity Inbox API (from OLD extension)
  async checkNewFollowers(): Promise<{ success: boolean; followers?: any[]; error?: string }> {
    try {
      const timestamp = new Date().toLocaleTimeString();
      console.log(`🔍 [${timestamp}] ➤ FOLLOWER CHECK STARTED - Checking Instagram Activity Inbox for new followers...`);
      
      // Get CSRF token for Instagram API calls
      const csrfToken = await this.getCsrfToken();
      if (!csrfToken) {
        throw new Error('Could not get CSRF token');
      }

      // Call Instagram Activity Inbox API (same as OLD extension)
      const inboxResponse = await fetch('https://i.instagram.com/api/v1/news/inbox/', {
        credentials: 'include',
        headers: {
          'accept': 'application/json, text/plain, */*',
          'Referer': 'https://www.instagram.com/',
          'Referrer-Policy': 'strict-origin-when-cross-origin',
          'x-asbd-id': '129477',
          'X-IG-App-ID': '936619743392459',
          'x-instagram-ajax': '1',
          'X-CSRFToken': csrfToken,
          'x-requested-with': 'XMLHttpRequest',
        },
        body: null,
        method: 'POST',
      });

      if (!inboxResponse.ok) {
        throw new Error(`Instagram API error: ${inboxResponse.status}`);
      }

      const inboxData = await inboxResponse.json();
      
      if (inboxData.status !== 'ok') {
        throw new Error(`Instagram API returned status: ${inboxData.status}`);
      }

      // Extract new followers from activity stories (like OLD extension - process ALL stories)
      const stories = inboxData.new_stories || [];
      console.log(`🔍 [${timestamp}] DEBUG: Found ${stories.length} total stories in inbox`);
      console.log(`🔍 [${timestamp}] DEBUG: First few stories:`, stories.slice(0, 3));
      
      const followers: any[] = [];

      // Filter to new followers like OLD extension (story_type 101, type 3 indicates new followers)
      for (const story of stories) {
        console.log(`🔍 [${timestamp}] DEBUG: Processing story - story_type: ${story.story_type}, type: ${story.type}`);
        
        // Check for new follower indicators like OLD extension
        if (story.story_type === 101 && story.type === 3) {
          console.log(`✅ [${timestamp}] DEBUG: Found NEW FOLLOWER story`);
          
          // Extract username and user ID from new Instagram API structure
          let username = null;
          let userId = null;
          
          // Method 1: Extract directly from story.args fields (most reliable)
          if (story.args) {
            // Check if profile_name and profile_id are directly available
            if (story.args.profile_name) {
              username = story.args.profile_name;
            }
            if (story.args.profile_id) {
              userId = story.args.profile_id;
            }
            
            // Check links array for user data
            if (!username && story.args.links && Array.isArray(story.args.links)) {
              for (const link of story.args.links) {
                if (link && link.type === 'user') {
                  username = link.username || link.name;
                  userId = link.id || link.user_id;
                  break;
                }
              }
            }
            
            // Parse rich_text field for username and ID
            if (!username && story.args.rich_text) {
              // Format: {lemlukas|000000|1|user?id=73560994007}
              const richTextMatch = story.args.rich_text.match(/\{([^|]+)\|[^|]+\|[^|]+\|user\?id=(\d+)\}/);
              if (richTextMatch) {
                username = richTextMatch[1];
                userId = richTextMatch[2];
              }
            }
          }
          
          // Method 2: Extract from story.args.destination as fallback
          if (!username && story.args && story.args.destination) {
            const usernameMatch = story.args.destination.match(/username=([^&]+)/);
            const userIdMatch = story.args.destination.match(/id=([^&]+)/);
            if (usernameMatch) username = usernameMatch[1];
            if (!userId && userIdMatch) userId = userIdMatch[1];
            
            // For clips_home?id=XXX format, extract the ID
            if (!userId) {
              const clipsMatch = story.args.destination.match(/clips_home\?id=([^&]+)/);
              if (clipsMatch) userId = clipsMatch[1];
            }
          }
          
          // Method 3: Extract from story.args.text if still no username
          if (!username && story.args && story.args.text) {
            // Extract username from text like 'setoraicom zaczął/zaczęła Cię obserwować.'
            // or Polish format: 'Użytkownik lemlukas rozpoczął obserwowanie Cię z Twojej rolki.'
            let textMatch = story.args.text.match(/^([a-zA-Z0-9._]+)\s/);
            if (!textMatch) {
              // Try Polish format where username comes after "Użytkownik "
              textMatch = story.args.text.match(/Użytkownik\s+([a-zA-Z0-9._]+)\s/);
            }
            if (textMatch) username = textMatch[1];
          }
          
          console.log(`✅ [${timestamp}] DEBUG: Found NEW FOLLOWER @${username} (ID: ${userId})`);
          
          if (username && userId) {
            followers.push({
              instagramUserId: username,
              nickname: username, 
              instagramId: userId,
              timestamp: Math.floor(Date.now() / 1000), // Use current timestamp
              followedAt: new Date(),
              collectedAt: new Date().toISOString()
            });
          } else {
            console.log(`⚠️ [${timestamp}] DEBUG: Could not extract username/ID from story:`, {
              hasArgs: !!story.args,
              destination: story.args?.destination,
              text: story.args?.text,
              extractedUsername: username,
              extractedUserId: userId
            });
          }
        } else {
          console.log(`🔍 [${timestamp}] DEBUG: Skipped story - not a new follower (story_type: ${story.story_type}, type: ${story.type})`);
        }
      }

      const endTime = new Date().toLocaleTimeString();
      console.log(`✅ [${endTime}] FOUND ${followers.length} NEW FOLLOWERS - Check completed`);
      return { 
        success: true, 
        followers 
      };

    } catch (error) {
      console.error('❌ Error checking new followers:', error);
      return { 
        success: false, 
        error: error.message 
      };
    }
  }

  // Get Instagram CSRF token (from OLD extension)
  private async getCsrfToken(): Promise<string | null> {
    try {
      // Method 1: Try to get from meta tags
      const metaToken = document.querySelector('meta[name="csrf-token"]');
      if (metaToken) {
        const content = metaToken.getAttribute('content');
        if (content) {
          console.log('✅ Got CSRF token from meta tag');
          return content;
        }
      }

      // Method 2: Try to get from cookies
      const cookies = document.cookie.split(';');
      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'csrftoken') {
          console.log('✅ Got CSRF token from cookie');
          return value;
        }
      }

      // Method 3: Try to extract from page scripts
      const scripts = document.querySelectorAll('script:not([src])');
      for (const script of scripts) {
        const content = script.textContent || '';
        const tokenMatch = content.match(/"csrf_token":"([^"]+)"/);
        if (tokenMatch) {
          console.log('✅ Got CSRF token from script');
          return tokenMatch[1];
        }
      }

      console.log('❌ Could not find CSRF token');
      return null;
    } catch (error) {
      console.error('❌ Error getting CSRF token:', error);
      return null;
    }
  }
}