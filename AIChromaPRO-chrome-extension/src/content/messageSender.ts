// Instagram Message Sender - Clean implementation using injected script approach (from OLD extension)
import browser from 'webextension-polyfill';

declare global {
  interface Window {
    instagramLocalStorageHandler?: boolean;
  }
}

export class InstagramMessageSender {
  private injectedScriptInitialized = false;
  
  // Check if user has zero messages (existing conversation detection)
  async getHaveZeroMessages(username: string): Promise<'yes' | 'no' | 'unknown'> {
    try {
      console.log(`🔍 [${new Date().toLocaleTimeString()}] Checking if @${username} has zero messages...`);
      
      // Wait 4 seconds like OLD extension
      await this.delay(4000);
      
      // Check if we're in a direct message conversation
      if (!window.location.href.includes('/direct')) {
        console.log("❌ Not in direct message conversation, can't check zero messages");
        return 'unknown';
      }
      
      // Look for messages table elements (like OLD extension)
      const messagesFound = document.querySelectorAll('[data-scope="messages_table"]');
      console.log(`🔍 Messages found: ${messagesFound.length}`);
      
      let result: 'yes' | 'no' | 'unknown';
      if (messagesFound.length <= 1) {
        result = 'yes'; // Zero actual messages (safe to send)
        console.log(`✅ [${new Date().toLocaleTimeString()}] @${username} has ZERO messages - safe to send`);
      } else {
        result = 'no'; // Has existing conversation
        console.log(`⚠️ [${new Date().toLocaleTimeString()}] @${username} has EXISTING conversation (${messagesFound.length} messages) - will skip`);
      }
      
      return result;
      
    } catch (error) {
      console.error('❌ Error checking zero messages:', error);
      return 'unknown';
    }
  }

  // Send message using old extension injected script approach
  async sendMessage(request: { username: string; message: string; type?: string }): Promise<{ success: boolean; error?: string }> {
    // Ensure injected script is loaded
    if (!this.injectedScriptInitialized) {
      console.log('🔄 Injected script not initialized, initializing now...');
      this.initializeInjectedScript();
      // Wait a moment for script to load
      await this.delay(2000);
    }
    try {
      const { username, message } = request;
      console.log(`📤 Sending message to @${username}: "${message}"`);

      // Resolve username to user ID (like original extension)
      const recipient_id = await this.resolveUsernameToUserId(username);
      if (!recipient_id) {
        console.log('❌ Failed to resolve username to user ID:', username);
        throw new Error(`Failed to resolve username '${username}' to a user ID.`);
      }
      console.log('✅ Resolved username to user ID:', { username, recipient_id });

      let viewerId = 'unknown';
      const cookies = document.cookie.split(';');
      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'ds_user_id' && value) {
          viewerId = value;
          break;
        }
      }
      console.log('Viewer ID from cookies:', viewerId);

      const urlMatch = window.location.pathname.match(/\/direct\/t\/(\d+)/);
      let threadId = urlMatch ? urlMatch[1] : 'unknown';
      console.log('Current URL analysis:', {
        pathname: window.location.pathname,
        urlMatch: urlMatch ? urlMatch[0] : null,
        threadId
      });
      
      if (threadId === 'unknown') {
        console.log('No existing thread found, creating new one for recipient:', recipient_id);
        const group_thread_resp = await this.createGroupThread(recipient_id);

        if (group_thread_resp && group_thread_resp.status === 'ok') {
          threadId = group_thread_resp.thread_id;
          console.log('✅ New thread created successfully:', threadId);
          
          // Wait a moment for thread to be fully created
          await this.delay(1000);
          console.log('✅ Thread creation completed, ready for messaging');
          
        } else {
          console.log('❌ Failed to create new thread:', group_thread_resp);
          throw new Error('Failed to create message thread');
        }
      } else {
        console.log('✅ Using existing thread:', threadId);
      }

      // Use smart timeout system with heartbeat monitoring
      const messagePromise = new Promise<boolean>((resolve, reject) => {
        let messageResolved = false;
        let lastHeartbeat = Date.now();
        let heartbeatActive = false;
        const HEARTBEAT_TIMEOUT = 15000; // 15s if no heartbeat
        const MAX_TIMEOUT = 45000; // 45s max if heartbeat active
        
        const messageHandler = (event: MessageEvent) => {
          console.log('📨 Content script received message event:', event.data);
          
          // Handle heartbeat signals
          if (event.data && event.data.type === 'INJECT_HEARTBEAT') {
            lastHeartbeat = Date.now();
            heartbeatActive = true;
            console.log(`💓 Heartbeat received: ${event.data.status || 'processing'}`);
            return;
          }
          
          // Handle message response
          if (event.data && event.data.type === 'INJECT_DISPATCH_DM_RESPONSE') {
            window.removeEventListener('message', messageHandler);
            messageResolved = true;
            
            if (event.data.ret === 1) {
              console.log('✅ Message sent successfully!');
              resolve(true);
            } else {
              console.error('❌ Message failed:', event.data);
              reject(new Error(`Message failed: ${event.data.error || event.data.status_code}`));
            }
          }
        };

        window.addEventListener('message', messageHandler);

        const messageRequest = {
          type: 'INJECT_DISPATCH_DM_REQUEST',
          thread_id: threadId,
          viewer_id: viewerId,
          user: { id: recipient_id, username: username },
          text: message,
          debug: true
        };

        console.log('📤 Content script sending message request:', messageRequest);
        window.postMessage(messageRequest, '*');

        // Smart timeout with heartbeat monitoring
        const startTime = Date.now();
        const checkTimeout = () => {
          if (messageResolved) return;
          
          const now = Date.now();
          const timeSinceStart = now - startTime;
          const timeSinceHeartbeat = now - lastHeartbeat;
          
          // If we have active heartbeat, allow up to MAX_TIMEOUT total
          // If no heartbeat received, fail after HEARTBEAT_TIMEOUT
          if (heartbeatActive && timeSinceStart > MAX_TIMEOUT) {
            console.error(`⏰ Message timeout - no response after ${MAX_TIMEOUT/1000}s (had heartbeat)`);
            window.removeEventListener('message', messageHandler);
            reject(new Error('Message timeout - taking too long despite heartbeat'));
          } else if (!heartbeatActive && timeSinceHeartbeat > HEARTBEAT_TIMEOUT) {
            console.error(`⏰ Message timeout - no heartbeat received within ${HEARTBEAT_TIMEOUT/1000}s`);
            window.removeEventListener('message', messageHandler);
            reject(new Error('Message timeout - injected script not responding'));
          } else {
            // Keep checking every 2 seconds
            setTimeout(checkTimeout, 2000);
          }
        };
        
        // Start timeout monitoring after 2 seconds
        setTimeout(checkTimeout, 2000);
      });

      const result = await messagePromise;
      console.log('Message sent successfully via injected script');
      
      return { success: true };

    } catch (error) {
      console.log('Error in sendMessage:', error);
      console.error('Error in sendMessage:', error);
      return { success: false, error: error.message };
    }
  }

  // Resolve username to user ID (from original extension)
  private async resolveUsernameToUserId(username: string): Promise<string | null> {
    try {
      console.log('Attempting to resolve username to user ID:', username);
      const profileUrl = `https://www.instagram.com/api/v1/users/web_profile_info/?username=${username}`;
      const response = await fetch(profileUrl, {
        credentials: 'include',
        headers: {
          'User-Agent': navigator.userAgent,
          'Accept': 'application/json',
          'x-ig-app-id': '936619743392459'
        }
      });
      
      if (response.ok) {
        const json = await response.json();
        const userId = json?.data?.user?.id;
        if (userId) {
          console.log(`Resolved ${username} to user ID: ${userId}`);
          return userId;
        }
      }
      console.log('Could not resolve username to user ID via web_profile_info API');
      return null;
    } catch (error) {
      console.log('Error in resolveUsernameToUserId:', error);
      return null;
    }
  }

  // Create group thread (EXACT copy from original extension)
  private async createGroupThread(recipient_id: string): Promise<any> {
    let u = 'https://i.instagram.com/api/v1/direct_v2/create_group_thread/';
    let csrf_token = await this.getCsrfToken();
    const s = { recipient_users: '["'.concat(recipient_id, '"]') };
    const l = new URLSearchParams([...Object.entries(s)]).toString();

    let response = await fetch(u, {
      credentials: 'include',
      headers: {
        accept: 'application/json, text/plain, */*',
        'content-type': 'application/x-www-form-urlencoded',
        Referer: 'https://www.instagram.com/',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        'x-asbd-id': '129477',
        'X-IG-App-ID': '936619743392459',
        'x-instagram-ajax': '1',
        'X-CSRFToken': csrf_token,
        'x-requested-with': 'XMLHttpRequest',
      },
      body: l,
      method: 'POST',
    });
    const data = await response.json();
    return data;
  }

  // Get Instagram CSRF token (from OLD extension)
  private async getCsrfToken(): Promise<string | null> {
    try {
      // Method 1: Try to get from meta tags
      const metaToken = document.querySelector('meta[name="csrf-token"]');
      if (metaToken) {
        const content = metaToken.getAttribute('content');
        if (content) {
          console.log('✅ Got CSRF token from meta tag');
          return content;
        }
      }

      // Method 2: Try to get from cookies
      const cookies = document.cookie.split(';');
      for (const cookie of cookies) {
        const [name, value] = cookie.trim().split('=');
        if (name === 'csrftoken') {
          console.log('✅ Got CSRF token from cookie');
          return value;
        }
      }

      // Method 3: Try to extract from page scripts
      const scripts = document.querySelectorAll('script:not([src])');
      for (const script of scripts) {
        const content = script.textContent || '';
        const tokenMatch = content.match(/"csrf_token":"([^"]+)"/);
        if (tokenMatch) {
          console.log('✅ Got CSRF token from script');
          return tokenMatch[1];
        }
      }

      console.log('❌ Could not find CSRF token');
      return null;
    } catch (error) {
      console.error('❌ Error getting CSRF token:', error);
      return null;
    }
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Verify if a specific message appears in the DOM (for enhanced recovery)
  async verifyMessageInDOM(username: string, expectedMessage: string): Promise<boolean> {
    try {
      console.log(`🔍 [${new Date().toLocaleTimeString()}] Verifying if message "${expectedMessage}" appears in DOM for @${username}`);
      
      // Wait a moment for DOM to update
      await this.delay(2000);
      
      // Check if we're in a direct message conversation
      if (!window.location.href.includes('/direct')) {
        console.log("❌ Not in direct message conversation, cannot verify message in DOM");
        return false;
      }
      
      // Look for message elements containing our expected text
      const messageSelectors = [
        '[data-scope="messages_table"] [dir="auto"]',
        '[role="log"] [data-testid*="message"]',
        '.x1n2onr6 [role="presentation"] [dir="auto"]',
        '[data-scope="messages_table"] span',
        '[role="log"] span'
      ];
      
      for (const selector of messageSelectors) {
        try {
          const messageElements = document.querySelectorAll(selector);
          
          for (const element of messageElements) {
            const elementText = element.textContent || element.innerText || '';
            
            // Check if this element contains our expected message
            if (elementText.trim() === expectedMessage.trim()) {
              console.log(`✅ Found expected message "${expectedMessage}" in DOM using selector: ${selector}`);
              return true;
            }
          }
        } catch (selectorError) {
          console.log(`⚠️ Selector failed (non-critical): ${selector}`, selectorError);
        }
      }
      
      // Additional check: look for any messages that contain part of our expected message
      // (in case of text wrapping or formatting differences)
      if (expectedMessage.length > 10) {
        const messageKeywords = expectedMessage.split(' ').filter(word => word.length > 3).slice(0, 3);
        
        for (const selector of messageSelectors) {
          try {
            const messageElements = document.querySelectorAll(selector);
            
            for (const element of messageElements) {
              const elementText = element.textContent || element.innerText || '';
              
              // Check if element contains multiple keywords from our message
              const foundKeywords = messageKeywords.filter(keyword => 
                elementText.toLowerCase().includes(keyword.toLowerCase())
              );
              
              if (foundKeywords.length >= 2) {
                console.log(`✅ Found message with ${foundKeywords.length}/${messageKeywords.length} keywords: ${foundKeywords.join(', ')}`);
                return true;
              }
            }
          } catch (selectorError) {
            console.log(`⚠️ Keyword check failed (non-critical): ${selector}`, selectorError);
          }
        }
      }
      
      console.log(`❌ Expected message "${expectedMessage}" not found in DOM`);
      return false;
      
    } catch (error) {
      console.error('❌ Error verifying message in DOM:', error);
      return false;
    }
  }

  // Initialize injected script (like old extension)
  initializeInjectedScript() {
    console.log('🔄 Initializing injected script for Instagram DM handling...');
    console.log('Script URL:', chrome.runtime.getURL('src/injected/instagram-dm-handler.js'));
    
    const script = document.createElement('script');
    script.src = chrome.runtime.getURL('src/injected/instagram-dm-handler.js');
    script.onload = () => {
      console.log('✅ Injected DM handler script loaded successfully');
      console.log('Checking if instagramLocalStorageHandler is available:', !!window.instagramLocalStorageHandler);
      this.injectedScriptInitialized = true;
      script.remove();
    };
    script.onerror = (error) => {
      console.error('❌ Failed to load injected DM handler script:', error);
      console.error('Error details:', error);
      this.injectedScriptInitialized = false;
      script.remove();
    };
    
    console.log('📝 Appending script to document head...');
    document.head.appendChild(script);
  }

}