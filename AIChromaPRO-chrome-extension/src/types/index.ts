// Chrome Extension Types
export interface ExtensionSettings {
  apiKey: string;
  baseUrl: string;
  organizationId?: string;
  isActive: boolean;
  maxActionsPerDay: number;
  dmBeforeBreakMin: number;
  dmBeforeBreakMax: number;
  breakTimeMin: number;
  breakTimeMax: number;
  delayBetweenDmsMin: number;
  delayBetweenDmsMax: number;
  naturalStopStart: string;
  naturalStopEnd: string;
}

export interface FollowerData {
  instagramUserId: string;
  nickname?: string;
  profilePhoto?: string;
  collectedAt?: string;
}

export interface AttackQueueItem {
  id: string;
  instagramUserId: string;
  nickname?: string;
  profilePhoto?: string;
  priority: number; // 1=new followers, 2=saas followups, 3=non-responders
  messageContent: string[];
  messageSource?: string; // "chrome_extension_batch" or "saas_followup"
  scheduledAt: string;
  status: 'pending' | 'sent' | 'failed';
}

export interface ExtensionState {
  isRunning: boolean;
  todayActions: number;
  consecutiveDMs?: number;
  lastActionTime?: number;
  lastActionDate?: string; // Track the date of last action for daily reset
  currentBreakEnd?: number;
  lastFollowerScrapeTime?: number;
  pendingMessages: number;
  scrapingStatus?: 'INITIAL_SCRAPING' | 'SCRAPING_COMPLETED' | 'UNKNOWN';
  scrapingProgress?: {
    current: number;
    target: number;
    isInitialScraping: boolean;
  };
  wasInNaturalBreak?: boolean;
  // Message processing progress
  currentMessageProgress?: {
    messageId: string;
    username: string;
    currentLineIndex: number;
    totalLines: number;
    nextLineTime?: number;
    isInDMPage: boolean;
  };
  // Wait time tracking
  waitTimeProgress?: {
    type: 'between_users' | 'main_loop' | 'message_line';
    startTime: number;
    endTime: number;
    description: string;
  };
}

// Instagram DOM Types
export interface InstagramFollower {
  username: string;
  profileUrl: string;
  isVerified: boolean;
  isPrivate: boolean;
  followerCount?: string;
}

export interface InstagramMessage {
  threadId: string;
  recipientUsername: string;
  messageText: string;
  timestamp: number;
}