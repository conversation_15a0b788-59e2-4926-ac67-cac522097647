// Injected script for deeper Instagram integration
// This script runs in the page context and can access Instagram's internal APIs

interface InstagramAPI {
  _getFollowersData?: () => Promise<any[]>;
  _getCurrentUser?: () => any;
  _getPageData?: () => any;
}

declare global {
  interface Window {
    __instagramAPI?: InstagramAPI;
    __aichromapro?: {
      collectFollowers: () => Promise<any[]>;
      getCurrentUser: () => any;
    };
  }
}

class InstagramPageInjector {
  private followersData: any[] = [];

  constructor() {
    this.init();
  }

  private init() {
    // Expose methods to content script
    window.__aichromapro = {
      collectFollowers: this.collectFollowers.bind(this),
      getCurrentUser: this.getCurrentUser.bind(this)
    };

    // Try to hook into Instagram's internal APIs
    this.hookIntoInstagramAPIs();
  }

  private hookIntoInstagramAPIs() {
    // Intercept XHR/fetch requests to follower endpoints
    this.interceptNetworkRequests();
    
    // Try to access React fiber for more reliable data extraction
    this.setupReactFiberAccess();
  }

  private interceptNetworkRequests() {
    // Intercept XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    XMLHttpRequest.prototype.open = function(method: string, url: string | URL, ...args: any[]) {
      if (typeof url === 'string' && url.includes('/graphql/')) {
        this.addEventListener('load', function() {
          try {
            const response = JSON.parse(this.responseText);
            if (response.data?.user?.edge_followed_by?.edges) {
              // This is followers data
              window.__aichromapro?.collectFollowers();
            }
          } catch (e) {
            // Ignore parsing errors
          }
        });
      }
      return originalXHROpen.call(this, method, url, ...args);
    };

    // Intercept fetch
    const originalFetch = window.fetch;
    window.fetch = async function(input: RequestInfo | URL, init?: RequestInit) {
      const response = await originalFetch(input, init);
      
      if (typeof input === 'string' && input.includes('/graphql/')) {
        const clonedResponse = response.clone();
        try {
          const data = await clonedResponse.json();
          if (data.data?.user?.edge_followed_by?.edges) {
            // This is followers data
            window.__aichromapro?.collectFollowers();
          }
        } catch (e) {
          // Ignore parsing errors
        }
      }
      
      return response;
    };
  }

  private setupReactFiberAccess() {
    // Try to access React DevTools global hook
    if (window.__REACT_DEVTOOLS_GLOBAL_HOOK__) {
      // Can potentially access React component state
      console.log('React DevTools detected - enhanced data extraction available');
    }
  }

  private async collectFollowers(): Promise<any[]> {
    const followers: any[] = [];

    try {
      // Method 1: Extract from current DOM
      const followerElements = document.querySelectorAll('div[role="dialog"] ul li');
      
      followerElements.forEach((element, index) => {
        try {
          const followerData = this.extractFollowerFromElement(element);
          if (followerData) {
            followers.push(followerData);
          }
        } catch (error) {
          console.error(`Error extracting follower ${index}:`, error);
        }
      });

      // Method 2: Try to access Instagram's state management
      const windowData = this.extractFromWindowData();
      if (windowData.length > 0) {
        followers.push(...windowData);
      }

      // Method 3: Try to access GraphQL cache
      const cacheData = this.extractFromGraphQLCache();
      if (cacheData.length > 0) {
        followers.push(...cacheData);
      }

    } catch (error) {
      console.error('Error collecting followers:', error);
    }

    // Remove duplicates
    const uniqueFollowers = this.removeDuplicates(followers);
    this.followersData.push(...uniqueFollowers);

    return uniqueFollowers;
  }

  private extractFollowerFromElement(element: Element): any | null {
    try {
      // Extract username
      const usernameLink = element.querySelector('a[href*="/"]') as HTMLAnchorElement;
      if (!usernameLink) return null;

      const username = usernameLink.href.split('/').filter(Boolean).pop();
      if (!username) return null;

      // Extract display name
      const nameElement = element.querySelector('span[dir="auto"]');
      const displayName = nameElement?.textContent?.trim();

      // Extract profile image
      const imageElement = element.querySelector('img') as HTMLImageElement;
      const profileImage = imageElement?.src;

      // Extract verification status
      const verificationElement = element.querySelector('svg[aria-label*="Verified"]');
      const isVerified = !!verificationElement;

      // Extract follower count if visible
      const followerCountElement = element.querySelector('span:contains("follower")');
      const followerCount = followerCountElement?.textContent;

      return {
        username,
        displayName,
        profileImage,
        isVerified,
        followerCount,
        profileUrl: `https://www.instagram.com/${username}/`,
        extractedAt: new Date().toISOString(),
        extractionMethod: 'dom'
      };
    } catch (error) {
      console.error('Error extracting follower from element:', error);
      return null;
    }
  }

  private extractFromWindowData(): any[] {
    const followers: any[] = [];

    try {
      // Try to find Instagram's internal data structures
      const scripts = document.querySelectorAll('script:not([src])');
      
      scripts.forEach(script => {
        const content = script.textContent || '';
        
        // Look for user data patterns
        if (content.includes('edge_followed_by') || content.includes('followers')) {
          try {
            // Try to parse as JSON
            const matches = content.match(/\{[^{}]*"edge_followed_by"[^{}]*\}/g);
            matches?.forEach(match => {
              try {
                const data = JSON.parse(match);
                if (data.edge_followed_by?.edges) {
                  data.edge_followed_by.edges.forEach((edge: any) => {
                    const node = edge.node;
                    if (node.username) {
                      followers.push({
                        username: node.username,
                        displayName: node.full_name,
                        profileImage: node.profile_pic_url,
                        isVerified: node.is_verified,
                        isPrivate: node.is_private,
                        profileUrl: `https://www.instagram.com/${node.username}/`,
                        extractedAt: new Date().toISOString(),
                        extractionMethod: 'window_data'
                      });
                    }
                  });
                }
              } catch (e) {
                // Ignore JSON parsing errors
              }
            });
          } catch (e) {
            // Ignore extraction errors
          }
        }
      });
    } catch (error) {
      console.error('Error extracting from window data:', error);
    }

    return followers;
  }

  private extractFromGraphQLCache(): any[] {
    const followers: any[] = [];

    try {
      // Try to access Apollo/Relay cache if available
      const apolloCache = (window as any).__APOLLO_CLIENT__?.cache;
      const relayStore = (window as any).__RELAY_STORE__;

      if (apolloCache) {
        // Try to extract from Apollo cache
        const cacheData = apolloCache.extract();
        Object.values(cacheData).forEach((item: any) => {
          if (item?.edge_followed_by?.edges) {
            item.edge_followed_by.edges.forEach((edge: any) => {
              const node = edge.node;
              if (node?.username) {
                followers.push({
                  username: node.username,
                  displayName: node.full_name,
                  profileImage: node.profile_pic_url,
                  isVerified: node.is_verified,
                  isPrivate: node.is_private,
                  profileUrl: `https://www.instagram.com/${node.username}/`,
                  extractedAt: new Date().toISOString(),
                  extractionMethod: 'graphql_cache'
                });
              }
            });
          }
        });
      }
    } catch (error) {
      console.error('Error extracting from GraphQL cache:', error);
    }

    return followers;
  }

  private getCurrentUser(): any {
    try {
      // Try multiple methods to get current user info
      
      // Method 1: Check Instagram's global objects
      const igGlobal = (window as any).ig || (window as any)._sharedData;
      if (igGlobal?.config?.viewer) {
        return {
          username: igGlobal.config.viewer.username,
          userId: igGlobal.config.viewer.id,
          fullName: igGlobal.config.viewer.full_name,
          profilePicUrl: igGlobal.config.viewer.profile_pic_url,
          extractionMethod: 'ig_global'
        };
      }

      // Method 2: Parse from script tags
      const scripts = document.querySelectorAll('script:not([src])');
      for (const script of scripts) {
        const content = script.textContent || '';
        const match = content.match(/"username":"([^"]+)"/);
        if (match) {
          return {
            username: match[1],
            extractionMethod: 'script_parsing'
          };
        }
      }

      // Method 3: Extract from URL or DOM elements
      const profileLink = document.querySelector('a[href*="/"] img[alt]') as HTMLImageElement;
      if (profileLink?.alt) {
        const username = profileLink.alt.replace(/'s profile picture/, '');
        return {
          username,
          extractionMethod: 'dom_extraction'
        };
      }

    } catch (error) {
      console.error('Error getting current user:', error);
    }

    return null;
  }

  private removeDuplicates(followers: any[]): any[] {
    const seen = new Set();
    return followers.filter(follower => {
      const key = follower.username;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }
}

// Initialize the injector
new InstagramPageInjector();