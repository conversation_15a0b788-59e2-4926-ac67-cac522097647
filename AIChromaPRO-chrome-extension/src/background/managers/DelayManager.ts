import { ExtensionSettings } from '../../types';

// Simple Delay Manager for all timing operations
export class DelayManager {
  static async wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  static getRandomInterval(min: number, max: number): number {
    return Math.floor(Math.random() * (max - min + 1)) + min;
  }


  static async waitBetweenUsers(settings: ExtensionSettings): Promise<number> {
    // Validate settings to prevent NaN - FIX for NaN delay issue
    const minDelay = (typeof settings.delayBetweenDmsMin === 'number' && !isNaN(settings.delayBetweenDmsMin)) 
      ? settings.delayBetweenDmsMin : 5;
    const maxDelay = (typeof settings.delayBetweenDmsMax === 'number' && !isNaN(settings.delayBetweenDmsMax)) 
      ? settings.delayBetweenDmsMax : 15;
      
    const waitTime = this.getRandomInterval(
      minDelay * 60000,
      maxDelay * 60000
    );
    
    console.log(`⏳ Starting ${Math.round(waitTime/60000)} minute wait before next user (${minDelay}-${maxDelay}min range)`);
    
    // Wait with periodic progress updates
    await this.waitWithCountdown(waitTime, 'before next user');
    
    console.log(`✅ Finished waiting ${Math.round(waitTime/60000)} minutes, ready for next user`);
    return waitTime;
  }

  static async waitBetweenMessageLines(): Promise<number> {
    // 5-35 seconds hardcoded for human-like behavior
    const delay = 5000 + Math.random() * 30000;
    console.log(`⏳ Starting ${Math.round(delay / 1000)}s wait before next message line`);
    
    // Wait with countdown for shorter delays
    await this.waitWithCountdown(delay, 'before next message line');
    
    console.log(`✅ Finished waiting ${Math.round(delay / 1000)}s, sending next message line`);
    return delay;
  }

  private static async waitWithCountdown(totalMs: number, description: string): Promise<void> {
    const startTime = Date.now();
    const endTime = startTime + totalMs;
    
    // For waits longer than 2 minutes, show periodic progress
    const showProgress = totalMs > 120000;
    
    if (showProgress) {
      // Show countdown every 30 seconds for long waits
      const progressInterval = setInterval(() => {
        const now = Date.now();
        const remaining = endTime - now;
        
        if (remaining <= 0) {
          clearInterval(progressInterval);
          return;
        }
        
        const minutesLeft = Math.floor(remaining / 60000);
        const secondsLeft = Math.floor((remaining % 60000) / 1000);
        
        if (minutesLeft > 0) {
          console.log(`⏳ Still waiting ${minutesLeft}m ${secondsLeft}s ${description}...`);
        } else {
          console.log(`⏳ Still waiting ${secondsLeft}s ${description}...`);
        }
      }, 30000); // Update every 30 seconds
      
      // Clear interval when wait completes
      setTimeout(() => clearInterval(progressInterval), totalMs);
    }
    
    // Actually wait
    await this.wait(totalMs);
  }

}