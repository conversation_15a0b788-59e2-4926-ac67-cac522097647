// Simple State Manager to prevent parallel executions
export class StateManager {
  private isProcessing = false;

  canProcess(): boolean {
    return !this.isProcessing;
  }

  startProcessing(): boolean {
    if (this.isProcessing) return false;
    this.isProcessing = true;
    return true;
  }

  stopProcessing(): void {
    this.isProcessing = false;
  }

  isCurrentlyProcessing(): boolean {
    return this.isProcessing;
  }
}