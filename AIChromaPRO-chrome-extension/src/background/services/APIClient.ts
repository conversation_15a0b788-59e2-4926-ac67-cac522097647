import { ExtensionSettings, AttackQueueItem, FollowerData } from '../../types';

export class APIClient {
  private settings: ExtensionSettings | null = null;

  updateSettings(settings: ExtensionSettings) {
    this.settings = settings;
  }

  getSettings(): ExtensionSettings | null {
    return this.settings;
  }

  // Safe settings getter with defaults to prevent NaN errors
  getSafeSettings(): ExtensionSettings {
    if (this.settings) {
      return this.settings;
    }
    
    // Return safe defaults if settings not loaded
    console.warn('⚠️ Settings not loaded, using defaults');
    return {
      apiKey: '', // No hardcoded API key - user must configure
      baseUrl: 'http://localhost:3000', // Default to local development
      isActive: false,
      maxActionsPerDay: 50,
      dmBeforeBreakMin: 7,
      dmBeforeBreakMax: 10,
      breakTimeMin: 10,
      breakTimeMax: 30,
      delayBetweenDmsMin: 5, // 5 minutes default
      delayBetweenDmsMax: 15, // 15 minutes default
      naturalStopStart: '22:00',
      naturalStopEnd: '08:00'
    };
  }

  async getAttackQueue(): Promise<AttackQueueItem[]> {
    if (!this.settings) return [];

    try {
      const response = await fetch(`${this.settings.baseUrl}/api/chrome-extension/attack-queue?apiKey=${this.settings.apiKey}&limit=1`);

      if (!response.ok) throw new Error(`API error: ${response.status}`);
      
      const data = await response.json();
      return data.messages || [];
    } catch (error) {
      console.error('Error fetching attack queue:', error);
      return [];
    }
  }

  async markMessageSent(messageId: string): Promise<void> {
    if (!this.settings) return;

    try {
      await fetch(`${this.settings.baseUrl}/api/chrome-extension/attack-queue`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          apiKey: this.settings.apiKey,
          messageId,
          action: 'mark_sent'
        })
      });
    } catch (error) {
      console.error('Error marking message as sent:', error);
    }
  }

  async markMessageFailed(messageId: string, errorMsg: string): Promise<void> {
    if (!this.settings) return;

    try {
      await fetch(`${this.settings.baseUrl}/api/chrome-extension/attack-queue`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          apiKey: this.settings.apiKey,
          messageId,
          action: 'mark_failed'
        })
      });
    } catch (error) {
      console.error('Error marking message as failed:', error);
    }
  }

  async markMessageSkipped(messageId: string): Promise<void> {
    if (!this.settings) return;

    try {
      await fetch(`${this.settings.baseUrl}/api/chrome-extension/attack-queue`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          apiKey: this.settings.apiKey,
          messageId,
          action: 'mark_skipped'
        })
      });
    } catch (error) {
      console.error('Error marking message as skipped:', error);
    }
  }

  async uploadFollowers(followers: FollowerData[], isInitialScrapingComplete = false): Promise<void> {
    if (!this.settings) return;

    try {
      const response = await fetch(`${this.settings.baseUrl}/api/chrome-extension/followers`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.settings.apiKey}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          apiKey: this.settings.apiKey,
          followers,
          isInitialScrapingComplete
        })
      });

      if (!response.ok) throw new Error(`Upload failed: ${response.status}`);
    } catch (error) {
      console.error('Error uploading followers:', error);
    }
  }

  async getRecentFollowerUsernames(): Promise<string[]> {
    if (!this.settings) return [];

    try {
      const response = await fetch(`${this.settings.baseUrl}/api/chrome-extension/followers?apiKey=${this.settings.apiKey}&usernamesOnly=true`);
      
      if (!response.ok) throw new Error(`API error: ${response.status}`);
      
      const data = await response.json();
      return data.followerUsernames || [];
    } catch (error) {
      console.error('Error fetching recent follower usernames:', error);
      return [];
    }
  }

  async getScrapingStatus(): Promise<any> {
    if (!this.settings) {
      console.error('❌ No settings available for API call');
      throw new Error('Extension settings not configured');
    }

    if (!this.settings.apiKey) {
      console.error('❌ No API key in settings');
      throw new Error('API key not configured');
    }

    try {
      const url = `${this.settings.baseUrl}/api/chrome-extension/settings?apiKey=${this.settings.apiKey}`;
      console.log('🌐 Fetching from:', url);
      
      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      console.log('📡 API Response status:', response.status);
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error('❌ API error response:', errorText);
        throw new Error(`API error ${response.status}: ${errorText || 'Unknown error'}`);
      }
      
      const data = await response.json();
      console.log('✅ API data received:', data);
      return data;
    } catch (error) {
      console.error('❌ Error in getScrapingStatus:', error);
      throw error; // Re-throw to be handled by caller
    }
  }

  async getUserProfile(): Promise<any> {
    if (!this.settings) return null;

    try {
      const response = await fetch(`${this.settings.baseUrl}/api/chrome-extension/settings?apiKey=${this.settings.apiKey}`);
      
      if (!response.ok) throw new Error(`API error: ${response.status}`);
      
      const data = await response.json();
      return data;
    } catch (error) {
      console.error('Error getting user profile:', error);
      return null;
    }
  }

  async updateScrapingStatus(status: 'INITIAL_SCRAPING' | 'SCRAPING_COMPLETED'): Promise<void> {
    if (!this.settings) {
      throw new Error('Extension settings not configured');
    }

    if (!this.settings.apiKey) {
      throw new Error('API key not configured');
    }

    try {
      console.log(`🔄 Updating scraping status to: ${status}`);
      
      const response = await fetch(`${this.settings.baseUrl}/api/chrome-extension/scraping-status`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          apiKey: this.settings.apiKey,
          scrapingStatus: status
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to update scraping status: ${response.status} - ${errorText}`);
      }

      console.log(`✅ Successfully updated scraping status to: ${status}`);
    } catch (error) {
      console.error('❌ Error updating scraping status:', error);
      throw error;
    }
  }
}