import browser from 'webextension-polyfill';
import { ExtensionSettings, ExtensionState, AttackQueueItem, FollowerData } from '../../types';
import { DelayManager } from '../managers/DelayManager';
import { StateManager } from '../managers/StateManager';
import { APIClient } from './APIClient';
import { ActionScheduler } from '../schedulers/ActionScheduler';
import { TaskQueue } from '../tasks/TaskQueue';

export class BackgroundService {
  private apiClient: APIClient;
  private scheduler: ActionScheduler;
  private taskQueue: TaskQueue;
  private stateManager: StateManager;
  private mainLoopInterval: any = null;
  private monitoringInterval: any = null;
  private messageQueueInterval: any = null;
  private followerCheckInterval: any = null;
  private followerUploadInterval: any = null;
  private midnightResetInterval: any = null;
  private mainLoopCountdownInterval: any = null;
  private settingsRefreshInterval: any = null;
  
  private state: ExtensionState = {
    isRunning: false,
    todayActions: 0,
    consecutiveDMs: 0,
    pendingMessages: 0,
    scrapingStatus: 'UNKNOWN', // Unknown until we check API
    scrapingProgress: {
      current: 0,
      target: 5000,
      isInitialScraping: false
    }
  };

  constructor() {
    this.apiClient = new APIClient();
    this.scheduler = new ActionScheduler();
    this.taskQueue = new TaskQueue();
    this.stateManager = new StateManager();
    this.init();
  }

  private async init() {
    // Load settings and state from storage
    await this.loadState();
    
    // Check and reset daily actions if it's a new day
    await this.checkAndResetDailyActions();
    
    // Load fresh settings from API on startup
    await this.loadApiSettings();
    
    // Set up periodic tasks
    this.setupPeriodicTasks();
    
    // Listen for messages from popup and content scripts
    browser.runtime.onMessage.addListener(this.handleMessage.bind(this));
  }

  private async loadApiSettings() {
    try {
      console.log('🔄 Loading settings from API...');
      // Use safe settings to ensure we have API key and base URL
      const safeSettings = this.apiClient.getSafeSettings();
      
      // Temporarily set safe settings to make API call
      this.apiClient.updateSettings(safeSettings);
      
      // Fetch real settings from API
      const apiSettings = await this.apiClient.getScrapingStatus();
      
      if (apiSettings) {
        // Merge API settings with safe defaults to ensure all required fields exist
        const completeSettings = {
          ...safeSettings,
          ...apiSettings,
          apiKey: safeSettings.apiKey, // Keep the API key from safe settings
          baseUrl: safeSettings.baseUrl  // Keep the base URL from safe settings
        };
        
        // Update with complete settings
        this.apiClient.updateSettings(completeSettings);
        console.log('✅ API settings loaded:', completeSettings);
      } else {
        console.log('⚠️ No API settings received, using defaults');
      }
    } catch (error) {
      console.error('❌ Failed to load API settings, using defaults:', error);
      // Continue with safe defaults if API fails
    }
  }

  private async refreshSettingsFromAPI() {
    try {
      console.log('🔄 Refreshing settings from API...');
      
      // Get current settings for comparison
      const currentSettings = this.apiClient.getSettings();
      if (!currentSettings?.apiKey) {
        console.log('⚠️ No API key available, skipping settings refresh');
        return;
      }
      
      // Fetch latest settings from API
      const apiSettings = await this.apiClient.getScrapingStatus();
      
      if (apiSettings) {
        // Create new settings object with API updates
        const newSettings = {
          ...currentSettings,
          ...apiSettings,
          apiKey: currentSettings.apiKey, // Keep existing API key
          baseUrl: currentSettings.baseUrl  // Keep existing base URL
        };
        
        // Check if settings actually changed
        const hasChanges = this.hasSettingsChanged(currentSettings, newSettings);
        
        if (hasChanges) {
          console.log('✅ Settings updated from API:', {
            maxActionsPerDay: newSettings.maxActionsPerDay,
            dmBeforeBreakMin: newSettings.dmBeforeBreakMin,
            dmBeforeBreakMax: newSettings.dmBeforeBreakMax,
            breakTimeMin: newSettings.breakTimeMin,
            breakTimeMax: newSettings.breakTimeMax,
            delayBetweenDmsMin: newSettings.delayBetweenDmsMin,
            delayBetweenDmsMax: newSettings.delayBetweenDmsMax,
            naturalStopStart: newSettings.naturalStopStart,
            naturalStopEnd: newSettings.naturalStopEnd
          });
          
          // Update settings in memory and storage
          this.apiClient.updateSettings(newSettings);
          await browser.storage.local.set({ settings: newSettings });
        } else {
          console.log('📊 Settings unchanged, no update needed');
        }
      } else {
        console.log('⚠️ No API settings received during refresh');
      }
    } catch (error) {
      console.error('❌ Failed to refresh settings from API:', error);
      // Don't throw error - just continue with existing settings
    }
  }

  private hasSettingsChanged(current: any, updated: any): boolean {
    const settingsToCheck = [
      'maxActionsPerDay',
      'dmBeforeBreakMin',
      'dmBeforeBreakMax',
      'breakTimeMin',
      'breakTimeMax',
      'delayBetweenDmsMin',
      'delayBetweenDmsMax',
      'naturalStopStart',
      'naturalStopEnd'
    ];
    
    return settingsToCheck.some(key => current[key] !== updated[key]);
  }

  private async loadState() {
    const stored = await browser.storage.local.get(['extensionState', 'settings']);
    if (stored.extensionState) {
      this.state = { ...this.state, ...stored.extensionState };
    }
    if (stored.settings) {
      this.apiClient.updateSettings(stored.settings);
    }
  }

  private async saveState() {
    await browser.storage.local.set({ extensionState: this.state });
  }

  private async checkAndResetDailyActions() {
    // Get today's date in a consistent format (YYYY-MM-DD)
    const today = new Date().toDateString();
    const lastActionDate = this.state.lastActionDate;
    
    console.log(`📅 Checking daily reset - Today: ${today}, Last action date: ${lastActionDate || 'never'}`);
    
    // If dates differ or no date recorded, reset daily counters
    if (!lastActionDate || lastActionDate !== today) {
      console.log(`🔄 New day detected! Resetting daily counters...`);
      console.log(`📊 Previous day stats: ${this.state.todayActions} actions completed`);
      
      // Reset daily counters
      this.state.todayActions = 0;
      this.state.consecutiveDMs = 0;
      this.state.currentBreakEnd = undefined;
      this.state.lastActionDate = today;
      
      await this.saveState();
      console.log(`✅ Daily counters reset for ${today}`);
    } else {
      console.log(`📊 Same day - keeping existing counters: ${this.state.todayActions} actions today`);
    }
  }

  private setupPeriodicTasks() {
    // Clear any existing intervals first
    this.clearPeriodicTasks();
    
    // Check for new messages to send every 2-5 minutes
    this.messageQueueInterval = setInterval(() => {
      if (this.state.isRunning && this.state.scrapingStatus !== 'INITIAL_SCRAPING' && this.shouldTakeAction()) {
        this.processMessageQueue();
      }
    }, DelayManager.getRandomInterval(120000, 300000)); // 2-5 minutes

    // Check for new followers every 30 seconds (only after initial scraping is complete)
    this.followerCheckInterval = setInterval(() => {
      console.log(`🔍 Follower check interval fired - isRunning: ${this.state.isRunning}, scrapingStatus: ${this.state.scrapingStatus}`);
      if (this.state.isRunning && this.state.scrapingStatus === 'SCRAPING_COMPLETED') {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`⏰ [${timestamp}] BACKGROUND: Checking for new followers...`);
        this.checkForNewFollowers();
      } else if (this.state.isRunning && this.state.scrapingStatus === 'INITIAL_SCRAPING') {
        console.log(`⏸️ Follower monitoring blocked - still in INITIAL_SCRAPING phase`);
      } else {
        console.log(`⏸️ Follower check skipped - extension not running`);
      }
    }, 30000); // 30 seconds

    // Upload followers every 10-15 minutes
    this.followerUploadInterval = setInterval(() => {
      if (this.state.isRunning) {
        this.processCollectedFollowers();
      }
    }, DelayManager.getRandomInterval(600000, 900000)); // 10-15 minutes

    // Refresh settings every 5-10 minutes to pick up dashboard changes
    this.settingsRefreshInterval = setInterval(() => {
      if (this.state.isRunning) {
        this.refreshSettingsFromAPI();
      }
    }, DelayManager.getRandomInterval(300000, 600000)); // 5-10 minutes

    // Reset daily action count at midnight
    this.midnightResetInterval = setInterval(() => {
      const now = new Date();
      if (now.getHours() === 0 && now.getMinutes() === 0) {
        this.state.todayActions = 0;
        this.state.consecutiveDMs = 0; // Reset consecutive DM counter at midnight
        this.state.currentBreakEnd = undefined; // Clear any ongoing break
        this.state.lastActionDate = now.toDateString(); // Update last action date to current day
        this.saveState();
        console.log(`🌙 Midnight reset completed for ${now.toDateString()}`);
      }
    }, 60000); // Check every minute
    
    console.log('✅ All periodic tasks set up successfully');
  }

  private clearPeriodicTasks() {
    // Clear all interval references
    const intervals = [
      { name: 'messageQueueInterval', ref: this.messageQueueInterval },
      { name: 'followerCheckInterval', ref: this.followerCheckInterval },
      { name: 'followerUploadInterval', ref: this.followerUploadInterval },
      { name: 'midnightResetInterval', ref: this.midnightResetInterval },
      { name: 'mainLoopCountdownInterval', ref: this.mainLoopCountdownInterval },
      { name: 'settingsRefreshInterval', ref: this.settingsRefreshInterval }
    ];
    
    for (const interval of intervals) {
      if (interval.ref) {
        console.log(`🔄 Clearing ${interval.name}...`);
        clearInterval(interval.ref);
      }
    }
    
    // Reset all interval references to null
    this.messageQueueInterval = null;
    this.followerCheckInterval = null;
    this.followerUploadInterval = null;
    this.midnightResetInterval = null;
    this.mainLoopCountdownInterval = null;
    this.settingsRefreshInterval = null;
    
    console.log('✅ All periodic tasks cleared');
  }

  private shouldTakeAction(): boolean {
    const settings = this.apiClient.getSafeSettings();

    console.log(`🔍 shouldTakeAction debug - Current stats:`, {
      todayActions: this.state.todayActions,
      maxActionsPerDay: settings.maxActionsPerDay,
      consecutiveDMs: this.state.consecutiveDMs,
      currentBreakEnd: this.state.currentBreakEnd ? new Date(this.state.currentBreakEnd).toLocaleTimeString() : 'none',
      naturalStopStart: settings.naturalStopStart,
      naturalStopEnd: settings.naturalStopEnd
    });

    // Check daily limit
    if (this.state.todayActions >= settings.maxActionsPerDay) {
      console.log(`❌ Daily limit reached: ${this.state.todayActions}/${settings.maxActionsPerDay}`);
      return false;
    }

    // Check if we're in a break period
    if (this.state.currentBreakEnd && Date.now() < this.state.currentBreakEnd) {
      const remainingBreak = Math.round((this.state.currentBreakEnd - Date.now()) / 60000);
      if (remainingBreak > 0) {
        console.log(`😴 Still on break for ${remainingBreak} more minutes (ends at ${new Date(this.state.currentBreakEnd).toLocaleTimeString()})`);
      }
      return false;
    }
    
    // Clear expired break
    if (this.state.currentBreakEnd && Date.now() >= this.state.currentBreakEnd) {
      console.log(`✅ Break ended, resuming automation`);
      this.state.currentBreakEnd = undefined;
      this.saveState();
    }

    // Check natural stop time
    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    
    console.log(`🕐 Time check: Current=${currentTime}, StopWindow=${settings.naturalStopStart}-${settings.naturalStopEnd}`);
    
    if (settings.naturalStopStart <= settings.naturalStopEnd) {
      // Same day range (e.g., 21:00-23:59) - should STOP during this range
      if (currentTime >= settings.naturalStopStart && currentTime <= settings.naturalStopEnd) {
        console.log(`❌ In natural stop window (same day): ${currentTime} is between ${settings.naturalStopStart}-${settings.naturalStopEnd}`);
        return false;
      }
      console.log(`✅ Outside stop window (same day): ${currentTime} is NOT between ${settings.naturalStopStart}-${settings.naturalStopEnd}`);
    } else {
      // Overnight range (e.g., 23:00-07:00) - should STOP during this range  
      if (currentTime >= settings.naturalStopStart || currentTime <= settings.naturalStopEnd) {
        const isLateEvening = currentTime >= settings.naturalStopStart;
        const isEarlyMorning = currentTime <= settings.naturalStopEnd;
        console.log(`❌ In natural stop window (overnight): ${currentTime} is in ${settings.naturalStopStart}-${settings.naturalStopEnd} (late evening: ${isLateEvening}, early morning: ${isEarlyMorning})`);
        return false;
      }
      console.log(`✅ Outside stop window (overnight): ${currentTime} is NOT in ${settings.naturalStopStart}-${settings.naturalStopEnd}`);
    }

    console.log(`✅ All checks passed - should take action`);
    return true;
  }

  // Add public method to access apiClient for external usage
  public getAPIClient(): APIClient {
    return this.apiClient;
  }

  // Add public method to access state
  public getState(): ExtensionState {
    return this.state;
  }

  // The rest of the methods would continue here...
  // For brevity, I'm showing the structure. The remaining methods from the original
  // BackgroundService class would be moved here following the same pattern.
  
  private startMainLoop() {
    console.log('🔄 Starting main loop for message processing...');
    
    // Clear any existing interval
    if (this.mainLoopInterval) {
      console.log('🔄 Clearing existing main loop interval before starting new one...');
      clearInterval(this.mainLoopInterval);
      this.mainLoopInterval = null;
    }
    
    // Clear any processing locks before starting
    if (this.stateManager.isCurrentlyProcessing()) {
      console.log('🔓 Clearing processing lock before starting main loop...');
      this.stateManager.stopProcessing();
    }
    
    // Run immediately and then every minute
    this.processMessages();
    this.mainLoopInterval = setInterval(() => {
      if (this.state.isRunning) {
        console.log('🔄 Main loop cycle - checking for messages...');
        this.processMessages();
      }
    }, 60000); // Every minute
    
    // Set up main loop countdown
    this.startMainLoopCountdown();
    
    console.log('✅ Main loop started - checking for messages every minute');
  }

  private startMainLoopCountdown() {
    // Show countdown to next main loop check every 15 seconds
    const countdownInterval = setInterval(() => {
      if (!this.state.isRunning) {
        clearInterval(countdownInterval);
        return;
      }
      
      // Calculate time until next minute boundary
      const now = new Date();
      const nextMinute = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours(), now.getMinutes() + 1, 0, 0);
      const secondsUntilNext = Math.floor((nextMinute.getTime() - now.getTime()) / 1000);
      
      if (secondsUntilNext <= 45 && secondsUntilNext > 0) {
        console.log(`📊 Next main loop check in ${secondsUntilNext} seconds...`);
      }
    }, 15000); // Check every 15 seconds
    
    // Store interval for cleanup
    if (!this.mainLoopCountdownInterval) {
      this.mainLoopCountdownInterval = countdownInterval;
    }
  }

  private async processMessages() {
    if (!this.state.isRunning) {
      return;
    }

    // Block message automation during initial scraping
    if (this.state.scrapingStatus === 'INITIAL_SCRAPING') {
      console.log('⏸️ Blocking message automation - still in INITIAL_SCRAPING phase');
      return;
    }

    // Check if we should take action (daily limit, break time, natural stop hours)
    if (!this.shouldTakeAction()) {
      console.log('⏸️ Not taking action due to limits (daily limit, break time, or natural stop hours)');
      return;
    }

    // Prevent parallel executions
    if (!this.stateManager.startProcessing()) {
      console.log('⏸️ Message processing already in progress, skipping...');
      return;
    }

    try {
      // Check if we're in middle of processing a message
      if (this.state.currentMessageProgress) {
        await this.continueMessageProgress();
        return;
      }

      console.log('🔍 Checking for messages to send...');
      
      // Get messages to send from API
      const messages = await this.apiClient.getAttackQueue();
      
      if (messages.length === 0) {
        console.log('📭 No messages to send - but ensuring Instagram tab is available for monitoring');
        
        // Still open Instagram tab for monitoring, even if no messages
        let tabs = await browser.tabs.query({ url: 'https://www.instagram.com/*' });
        if (tabs.length === 0) {
          console.log('📱 Opening Instagram tab for monitoring...');
          await browser.tabs.create({
            url: 'https://www.instagram.com/',
            active: false // Don't steal focus
          });
          console.log('✅ Instagram tab opened and ready for monitoring');
        } else {
          console.log('✅ Instagram tab already available');
        }
        return;
      }

      console.log(`📤 Found ${messages.length} messages to send`);
      
      // Show queue status
      if (messages.length > 1) {
        console.log(`🔄 Message queue status: Processing 1/${messages.length} users`);
      }
      
      // Start processing the first message
      const message = messages[0];
      await this.startMessageProcessing(message);
      
    } catch (error) {
      console.error('❌ Error in processMessages:', error);
    } finally {
      // Always release the processing lock
      this.stateManager.stopProcessing();
    }
  }

  private async startMessageProcessing(message: AttackQueueItem) {
    try {
      // Get or create Instagram tab
      let tabs = await browser.tabs.query({ url: 'https://www.instagram.com/*' });
      let tab;
      
      if (tabs.length > 0) {
        tab = tabs[0];
        // Make sure tab is active
        await browser.tabs.update(tab.id!, { 
          active: false,
          url: 'https://www.instagram.com/' // Ensure we're on main page
        });
        await this.waitForTabLoad(tab.id!);
        await DelayManager.wait(3000);
      } else {
        console.log('📱 Creating new Instagram tab for message processing...');
        tab = await browser.tabs.create({
          url: 'https://www.instagram.com/',
          active: false
        });
        await this.waitForTabLoad(tab.id!);
        await DelayManager.wait(5000); // Extra wait for login/content script
      }
      
      // Ensure content script is ready
      try {
        await this.waitForContentScript(tab.id!);
        console.log('✅ Content script ready, processing messages...');
      } catch (error) {
        console.log('🔄 Content script failed, refreshing Instagram tab...');
        await browser.tabs.reload(tab.id!);
        await DelayManager.wait(5000);
        try {
          await this.waitForContentScript(tab.id!);
          console.log('✅ Content script ready after refresh');
        } catch (retryError) {
          throw new Error(`Content script connection failed: ${retryError instanceof Error ? retryError.message : 'Unknown error'}`);
        }
      }

      console.log(`📤 Starting message processing for @${message.nickname}`);
      
      // Step 1: Navigate to profile
      console.log(`📍 Navigating to profile: @${message.nickname}`);
      const profileUrl = `https://www.instagram.com/${message.nickname}/`;
      await browser.tabs.update(tab.id!, { 
        url: profileUrl,
        active: false
      });
      await this.waitForTabLoad(tab.id!);
      await DelayManager.wait(4000);

      // Step 2: Click Message Button
      console.log(`📱 Clicking message button for @${message.nickname}`);
      await this.sendMessageWithRetry(
        tab.id!,
        {
          action: 'VISIT_USER_MESSAGES',
          username: message.nickname
        },
        `Visit Messages for ${message.nickname}`
      );
      await DelayManager.wait(6000); // Wait for DM page to load

      // Step 2.5: Check if user has existing conversation (zero message detection)
      // Only check for new followers (priority 1), skip for followups (priority 2+)
      const isNewFollower = message.priority === 1 || message.messageSource === 'chrome_extension_batch';
      
      if (isNewFollower) {
        console.log(`🔍 NEW FOLLOWER: Checking if @${message.nickname} has existing conversation...`);
        try {
          const zeroMessagesResult = await this.sendMessageWithRetry(
            tab.id!,
            {
              action: 'CHECK_ZERO_MESSAGES',
              username: message.nickname
            },
            `Check Zero Messages for ${message.nickname}`
          );

          if (zeroMessagesResult && (zeroMessagesResult as any).result === 'no') {
            // User has existing conversation - skip and mark as SKIPPED
            console.log(`⚠️ [${new Date().toLocaleTimeString()}] @${message.nickname} has EXISTING conversation - marking as SKIPPED`);
            await this.apiClient.markMessageSkipped(message.id);
            console.log(`✅ Successfully marked @${message.nickname} as SKIPPED`);
            return; // Skip this message
          } else {
            console.log(`✅ [${new Date().toLocaleTimeString()}] @${message.nickname} has ZERO messages - proceeding with message`);
          }
        } catch (error) {
          console.error(`❌ Error checking zero messages for @${message.nickname}:`, error);
          // Continue with message sending if check fails
        }
      } else {
        console.log(`📩 FOLLOWUP: Skipping zero-message check for @${message.nickname} (priority: ${message.priority}, source: ${message.messageSource}) - proceeding with followup message`);
      }

      // Set up message progress state
      const messageTexts = message.messageContent || [];
      this.state.currentMessageProgress = {
        messageId: message.id,
        username: message.nickname || 'unknown',
        currentLineIndex: 0,
        totalLines: messageTexts.length,
        isInDMPage: true
      };
      
      await this.saveState();
      
      // Send first message immediately
      await this.sendCurrentMessageLine();
      
    } catch (error) {
      console.error(`❌ Error starting message processing for @${message.nickname}:`, error);
      await this.apiClient.markMessageFailed(message.id, error instanceof Error ? error.message : 'Unknown error');
      this.clearMessageProgress();
    }
  }

  private async continueMessageProgress() {
    const progress = this.state.currentMessageProgress!;
    
    // Check if it's time to send next message
    if (progress.nextLineTime && Date.now() < progress.nextLineTime) {
      console.log(`⏳ Still waiting to send message line ${progress.currentLineIndex + 1}/${progress.totalLines} to @${progress.username}`);
      return;
    }
    
    console.log(`📤 Continuing message progress for @${progress.username} (line ${progress.currentLineIndex + 1}/${progress.totalLines})`);
    
    try {
      await this.sendCurrentMessageLine();
    } catch (error) {
      console.error(`❌ Error continuing message progress:`, error);
      await this.apiClient.markMessageFailed(progress.messageId, error instanceof Error ? error.message : 'Unknown error');
      this.clearMessageProgress();
    }
  }

  private async sendCurrentMessageLine() {
    const progress = this.state.currentMessageProgress!;
    
    // Get current message from API
    const messages = await this.apiClient.getAttackQueue();
    const currentMessage = messages.find(m => m.id === progress.messageId);
    
    if (!currentMessage) {
      console.log(`📭 Message ${progress.messageId} no longer in queue, completing processing`);
      this.clearMessageProgress();
      return;
    }
    
    const messageTexts = currentMessage.messageContent || [];
    const currentText = messageTexts[progress.currentLineIndex];
    
    if (!currentText) {
      console.log(`✅ All messages sent to @${progress.username}, marking as complete`);
      await this.apiClient.markMessageSent(progress.messageId);
      this.state.todayActions++;
      this.state.lastActionDate = new Date().toDateString(); // Update last action date
      this.clearMessageProgress();
      return;
    }
    
    console.log(`📤 Sending message line ${progress.currentLineIndex + 1}/${progress.totalLines} to @${progress.username}: "${currentText}"`);
    
    // Track the message we're about to send for recovery verification
    progress.lastSentMessage = currentText;
    progress.lastError = null; // Clear previous error
    await this.saveState();
    
    // Get Instagram tab
    const tabs = await browser.tabs.query({ url: 'https://www.instagram.com/*' });
    if (tabs.length === 0) {
      throw new Error('Instagram tab not found');
    }
    
    const tab = tabs[0];
    
    // Send the message
    try {
      const sendResult = await this.sendMessageWithRetry(
        tab.id!,
        {
          action: 'SEND_MESSAGE',
          username: progress.username,
          message: currentText,
          type: 'normal'
        },
        `Send Message ${progress.currentLineIndex + 1}/${progress.totalLines} to ${progress.username}`
      );

      if (sendResult && (sendResult as any).success) {
        console.log(`✅ Message line ${progress.currentLineIndex + 1}/${progress.totalLines} sent successfully`);
        
        // Move to next message line
        progress.currentLineIndex++;
        
        if (progress.currentLineIndex >= progress.totalLines) {
          // All messages sent
          console.log(`✅ All ${progress.totalLines} messages sent successfully to @${progress.username}`);
          await this.apiClient.markMessageSent(progress.messageId);
          this.state.todayActions++;
          this.state.lastActionDate = new Date().toDateString(); // Update last action date
          this.state.consecutiveDMs = (this.state.consecutiveDMs || 0) + 1;
          this.clearMessageProgress();
          
          // Check if we need a break after X consecutive DMs
          const settings = this.apiClient.getSafeSettings();
          const dmBeforeBreakMin = (typeof settings.dmBeforeBreakMin === 'number' && !isNaN(settings.dmBeforeBreakMin)) 
            ? settings.dmBeforeBreakMin : 7;
          const dmBeforeBreakMax = (typeof settings.dmBeforeBreakMax === 'number' && !isNaN(settings.dmBeforeBreakMax)) 
            ? settings.dmBeforeBreakMax : 10;
          
          const breakTrigger = DelayManager.getRandomInterval(dmBeforeBreakMin, dmBeforeBreakMax);
          
          if (this.state.consecutiveDMs >= breakTrigger) {
            console.log(`😴 Break time! Sent ${this.state.consecutiveDMs} consecutive DMs (trigger: ${breakTrigger})`);
            
            // Calculate break duration and set break end time
            const minBreak = (typeof settings.breakTimeMin === 'number' && !isNaN(settings.breakTimeMin)) ? settings.breakTimeMin : 10;
            const maxBreak = (typeof settings.breakTimeMax === 'number' && !isNaN(settings.breakTimeMax)) ? settings.breakTimeMax : 30;
            const breakDuration = DelayManager.getRandomInterval(minBreak * 60000, maxBreak * 60000);
            
            this.state.currentBreakEnd = Date.now() + breakDuration;
            this.state.consecutiveDMs = 0; // Reset counter after break starts
            await this.saveState();
            
            console.log(`😴 Starting ${Math.round(breakDuration/60000)} minute break (${minBreak}-${maxBreak}min range)`);
            console.log(`⏸️ Extension will pause until break ends at ${new Date(this.state.currentBreakEnd).toLocaleTimeString()}`);
          } else {
            console.log(`📊 Consecutive DMs: ${this.state.consecutiveDMs}/${breakTrigger} (break at ${breakTrigger})`);
            
            // Wait before next user (5-15 minutes based on settings)
            console.log(`⏳ Waiting between users before processing next message...`);
            await DelayManager.waitBetweenUsers(settings);
          }
        } else {
          // Schedule next message
          const delay = 5000 + Math.random() * 30000; // 5-35 seconds
          progress.nextLineTime = Date.now() + delay;
          console.log(`⏳ Scheduled next message line in ${Math.round(delay / 1000)}s`);
          await this.saveState();
        }
        
      } else {
        throw new Error('Message send failed');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Failed to send message line ${progress.currentLineIndex + 1}/${progress.totalLines}:`, error);
      
      // Track the error for enhanced recovery logic
      progress.lastError = errorMessage;
      await this.saveState();
      
      // Enhanced error classification and recovery logic
      const isTimeoutError = errorMessage.includes('timeout') || errorMessage.includes('timeout');
      const isConnectionError = errorMessage.includes('connection') || errorMessage.includes('network') || errorMessage.includes('fetch');
      const isCommunicationError = errorMessage.includes('Receiving end does not exist') || errorMessage.includes('sendMessage');
      
      console.log('🔍 Error classification:', {
        isTimeoutError,
        isConnectionError,
        isCommunicationError,
        errorMessage
      });
      
      // Try recovery for timeout and communication errors (most likely false negatives)
      if (isTimeoutError || isCommunicationError || errorMessage.includes('Message send failed')) {
        console.log('🔍 Potential false negative detected - attempting enhanced recovery...');
        
        // Use enhanced recovery logic
        progress.lastError = errorMessage; // Ensure error is tracked for heuristics
        const shouldContinue = await this.checkMessageSentAndContinue(progress);
        
        if (shouldContinue) {
          console.log('✅ Enhanced recovery SUCCESS - message was likely sent, continuing to next line');
          progress.currentLineIndex++;
          
          if (progress.currentLineIndex >= progress.totalLines) {
            // All messages completed
            console.log(`✅ All ${progress.totalLines} messages completed for @${progress.username} (recovered from ${isTimeoutError ? 'timeout' : 'communication'} error)`);
            await this.apiClient.markMessageSent(progress.messageId);
            this.state.todayActions++;
            this.state.lastActionDate = new Date().toDateString();
            this.clearMessageProgress();
          } else {
            // Schedule next message with shorter delay since we lost time
            progress.nextLineTime = Date.now() + 8000; // 8 seconds instead of 5-35
            console.log(`⏳ Scheduled next message line in 8s (recovered from ${isTimeoutError ? 'timeout' : 'communication'} error)`);
            await this.saveState();
          }
          return;
        } else {
          console.log('❌ Enhanced recovery FAILED - message was likely not sent');
        }
      } else {
        console.log('❌ Non-recoverable error type - marking as failed immediately');
      }
      
      // If not recoverable or recovery failed, mark as failed
      await this.apiClient.markMessageFailed(progress.messageId, errorMessage);
      this.clearMessageProgress();
    }
  }

  private async checkMessageSentAndContinue(progress: any): Promise<boolean> {
    try {
      console.log('🔍 Enhanced verification - checking if message was actually sent despite timeout...');
      
      // Wait longer for Instagram to process in case of network delays
      await DelayManager.wait(5000);
      
      // Get Instagram tab and check conversation
      const tabs = await browser.tabs.query({ url: 'https://www.instagram.com/*' });
      if (tabs.length === 0) {
        console.log('❌ No Instagram tab found for verification');
        return false;
      }
      
      const tab = tabs[0];
      
      // Method 1: Check if conversation now exists (original method)
      try {
        console.log('🔍 Method 1: Checking conversation existence...');
        const zeroMessagesResult = await this.sendMessageWithRetry(
          tab.id!,
          {
            action: 'CHECK_ZERO_MESSAGES',
            username: progress.username
          },
          'Verify Message Sent',
          2 // Only 2 retries for verification
        );
        
        // If we get a response and it shows "no" (has messages), 
        // then our message likely went through
        if (zeroMessagesResult && (zeroMessagesResult as any).result === 'no') {
          console.log('✅ Method 1 SUCCESS: Verification suggests message was sent (conversation exists)');
          return true;
        }
        
        console.log('🤷 Method 1: Conversation state unclear, trying more verification methods...');
        
      } catch (verifyError) {
        console.log('⚠️ Method 1 failed, trying alternative methods:', verifyError);
      }

      // Method 2: Try to send a ping to content script to check DOM state
      try {
        console.log('🔍 Method 2: Checking DOM state via content script...');
        const domCheckResult = await this.sendMessageWithRetry(
          tab.id!,
          {
            action: 'VERIFY_MESSAGE_IN_DOM',
            username: progress.username,
            expectedMessage: progress.lastSentMessage // We'll need to track this
          },
          'DOM Verification',
          1 // Single retry for DOM check
        );
        
        if (domCheckResult && (domCheckResult as any).messageFound) {
          console.log('✅ Method 2 SUCCESS: Message found in DOM - was sent successfully');
          return true;
        }
        
        console.log('🤷 Method 2: Message not found in DOM');
        
      } catch (domError) {
        console.log('⚠️ Method 2 failed:', domError);
      }

      // Method 3: Heuristic based on timing and error type
      try {
        console.log('🔍 Method 3: Heuristic analysis...');
        
        // If we've made it this far and it was a timeout (not connection error),
        // and we're dealing with a simple text message, there's a good chance it went through
        const errorWasTimeout = progress.lastError && progress.lastError.includes('timeout');
        const messageWasSimple = progress.lastSentMessage && progress.lastSentMessage.length < 500;
        
        if (errorWasTimeout && messageWasSimple) {
          console.log('✅ Method 3 SUCCESS: Timeout + simple message = likely sent successfully');
          console.log('📊 Heuristic factors: timeout error + simple message suggests false negative');
          return true;
        }
        
        console.log('🤷 Method 3: Heuristics don\'t suggest message was sent');
        
      } catch (heuristicError) {
        console.log('⚠️ Method 3 failed:', heuristicError);
      }

      console.log('❌ All verification methods failed - message likely not sent');
      return false;
      
    } catch (error) {
      console.error('❌ Error in enhanced message verification:', error);
      return false;
    }
  }

  private clearMessageProgress() {
    this.state.currentMessageProgress = undefined;
    this.saveState();
    console.log('🧹 Cleared message progress state');
  }

  private async waitForTabLoad(tabId: number): Promise<void> {
    return new Promise((resolve) => {
      const listener = (tabIdUpdated: number, changeInfo: any) => {
        if (tabIdUpdated === tabId && changeInfo.status === 'complete') {
          browser.tabs.onUpdated.removeListener(listener);
          resolve();
        }
      };
      browser.tabs.onUpdated.addListener(listener);
      
      // Timeout after 10 seconds
      setTimeout(() => {
        browser.tabs.onUpdated.removeListener(listener);
        resolve();
      }, 10000);
    });
  }

  private async waitForContentScript(tabId: number): Promise<void> {
    const maxAttempts = 5;
    const delay = 2000; // 2 seconds between attempts

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        console.log(`🔄 Attempt ${attempt}/${maxAttempts} - Testing content script...`);
        
        // Send a ping message to test if content script is ready
        const response = await browser.tabs.sendMessage(tabId, { action: 'PING' });
        
        if (response?.success) {
          console.log('✅ Content script is ready!');
          return;
        }
      } catch (error) {
        console.log(`❌ Attempt ${attempt} failed:`, error instanceof Error ? error.message : 'Unknown error');
        
        if (attempt < maxAttempts) {
          console.log(`⏳ Waiting ${delay}ms before next attempt...`);
          await DelayManager.wait(delay);
        }
      }
    }

    throw new Error(`Content script not ready after ${maxAttempts} attempts`);
  }

  private async sendMessageWithRetry(tabId: number, message: any, operationName: string, maxRetries = 3) {
    let lastError = null;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        // Check if tab still exists
        await browser.tabs.get(tabId);
        
        const result = await browser.tabs.sendMessage(tabId, message);
        return result;
      } catch (error: any) {
        lastError = error;
        
        if (attempt === maxRetries) {
          break;
        }
        
        // Try to recover based on error type
        if (error.message?.includes('Receiving end does not exist')) {
          const delayMs = 3000 + (attempt * 2000);
          console.log(`Content script not ready, waiting ${delayMs}ms before retry ${attempt}/${maxRetries}`);
          await DelayManager.wait(delayMs);
        } else {
          const delayMs = 1000 * attempt;
          await DelayManager.wait(delayMs);
        }
      }
    }
    
    const finalError = lastError || new Error(`${operationName} failed after ${maxRetries} attempts`);
    throw finalError;
  }

  private async processMessageQueue() {
    // Legacy method - now using processMessages
    return this.processMessages();
  }

  private async processCollectedFollowers() {
    // Placeholder for follower collection - not needed for basic messaging
  }

  private async handleMessage(request: any, sender: browser.Runtime.MessageSender) {
    switch (request.action) {
      case 'START_EXTENSION':
        return await this.startExtension();

      case 'STOP_EXTENSION':
        this.state.isRunning = false;
        await this.stopMainLoop(); // Stop main loop and monitoring
        await this.saveState();
        return { success: true };

      case 'GET_STATE':
        return { state: this.state };

      case 'UPDATE_SETTINGS':
        await browser.storage.local.set({ settings: request.data });
        this.apiClient.updateSettings(request.data);
        return { success: true };

      case 'CHECK_SCRAPING_STATUS':
        return await this.checkScrapingStatus();

      case 'START_INITIAL_SCRAPING':
        return await this.startInitialScraping();

      case 'SCRAPING_PROGRESS_UPDATE':
        this.updateScrapingProgress(request.data);
        return { success: true };

      case 'INITIAL_SCRAPING_COMPLETE':
        return await this.completeInitialScraping();

      case 'INITIAL_SCRAPING_FAILED':
        console.error('❌ Initial scraping failed:', request.data?.error);
        this.state.isRunning = false;
        await this.saveState();
        return { success: true };

      case 'UPLOAD_FOLLOWER_BATCH':
        return await this.uploadFollowerBatch(request.data);

      case 'MESSAGE_SENT':
        // Mark message as sent in API
        await this.apiClient.markMessageSent(request.data.queueItemId);
        return { success: true };

      case 'MESSAGE_FAILED':
        // Mark message as failed in API
        await this.apiClient.markMessageFailed(request.data.queueItemId, request.data.error);
        return { success: true };

      default:
        return { error: 'Unknown action' };
    }
  }

  private async startExtension() {
    try {
      // STEP 1: Force cleanup any lingering state before starting fresh
      console.log('🧹 Performing comprehensive startup cleanup...');
      
      // Clear any existing processing locks
      if (this.stateManager.isCurrentlyProcessing()) {
        console.log('🔓 Clearing existing processing lock...');
        this.stateManager.stopProcessing();
      }
      
      // Clear any existing intervals
      if (this.mainLoopInterval) {
        console.log('🔄 Clearing existing main loop interval...');
        clearInterval(this.mainLoopInterval);
        this.mainLoopInterval = null;
      }
      
      if (this.monitoringInterval) {
        console.log('📊 Clearing existing monitoring interval...');
        clearInterval(this.monitoringInterval);
        this.monitoringInterval = null;
      }
      
      // Clear all periodic task intervals  
      this.clearPeriodicTasks();
      
      // Close any existing Instagram tabs for fresh start
      try {
        const existingInstagramTabs = await browser.tabs.query({ url: 'https://www.instagram.com/*' });
        console.log(`📱 Found ${existingInstagramTabs.length} existing Instagram tabs to close`);
        
        for (const tab of existingInstagramTabs) {
          if (tab.id) {
            await browser.tabs.remove(tab.id);
            console.log(`✅ Closed existing Instagram tab: ${tab.id}`);
          }
        }
      } catch (error) {
        console.error('⚠️ Error closing existing Instagram tabs:', error);
      }
      
      // Wait a moment for cleanup to complete
      await DelayManager.wait(2000);
      console.log('✅ Startup cleanup completed');
      
      // STEP 2: Validate settings first
      const settings = this.apiClient.getSettings();
      if (!settings || !settings.apiKey) {
        return { success: false, error: 'Please configure API key first' };
      }

      console.log('🔄 Starting extension with API key check...');
      
      // Check scraping status from API
      const statusResult = await this.checkScrapingStatus();
      
      if (!statusResult.success) {
        console.error('❌ API connection failed:', statusResult.error);
        return { success: false, error: `API connection failed: ${statusResult.error}` };
      }

      console.log('✅ API connection successful. Scraping status:', this.state.scrapingStatus);

      this.state.isRunning = true;
      
      // Restart periodic tasks after cleanup (follower checking, uploads, etc.)
      console.log('🔄 Restarting periodic tasks after cleanup...');
      this.setupPeriodicTasks();
      
      // Start the appropriate workflow based on scraping status
      if (this.state.scrapingStatus === 'SCRAPING_COMPLETED') {
        console.log('🔄 Doing catch-up scraping to find any missed followers...');
        const catchUpResult = await this.doCatchUpScraping();
        
        if (catchUpResult.success) {
          if (catchUpResult.newFollowersFound > 0) {
            console.log(`✅ Catch-up complete! Found ${catchUpResult.newFollowersFound} new followers`);
          } else {
            console.log('✅ Catch-up complete! No new followers found - already up to date');
          }
        } else {
          console.log('⚠️ Catch-up scraping failed, continuing with automation anyway:', catchUpResult.error);
        }
        
        // Add delay before starting main loop to let catch-up operations settle
        console.log('⏳ Waiting 5 seconds before starting main automation loop...');
        await DelayManager.wait(5000);
        
        console.log('🔄 Starting main automation loop for message processing...');
        this.startMainLoop();
      } else {
        console.log('🔄 Initial scraping needed - starting scraping workflow...');
        const scrapingResult = await this.startInitialScraping();
        
        if (scrapingResult.success) {
          console.log('✅ Initial scraping completed successfully');
          console.log('🔄 Now starting main automation loop for message processing...');
          this.startMainLoop();
        } else {
          console.error('❌ Initial scraping failed:', scrapingResult.error);
          this.state.isRunning = false;
          await this.saveState();
          return { success: false, error: `Initial scraping failed: ${scrapingResult.error}` };
        }
      }
      
      await this.saveState();
      return { success: true, scrapingStatus: this.state.scrapingStatus };
    } catch (error) {
      console.error('❌ Error starting extension:', error);
      this.state.isRunning = false;
      await this.saveState();
      return { success: false, error: error.message || 'Unknown error occurred' };
    }
  }

  private async checkScrapingStatus() {
    try {
      console.log('🔍 Checking scraping status from API...');
      
      const status = await this.apiClient.getScrapingStatus();
      if (status) {
        console.log('✅ Received status from API:', status);
        
        // Handle missing scrapingStatus (existing records created before this field)
        this.state.scrapingStatus = status.scrapingStatus || 'SCRAPING_COMPLETED';
        
        // Update progress and store Instagram username
        this.state.scrapingProgress = {
          ...this.state.scrapingProgress,
          isInitialScraping: this.state.scrapingStatus === 'INITIAL_SCRAPING'
        };
        console.log('📊 Updated scraping progress:', this.state.scrapingProgress);
        console.log('📱 Instagram username:', status.instagramUsername);
        
        await this.saveState();
        return { success: true, status };
      }
      
      console.error('❌ API returned null/empty status');
      return { success: false, error: 'API returned empty response' };
    } catch (error) {
      console.error('❌ Error checking scraping status:', error);
      
      let errorMessage = 'Unknown error';
      if (error instanceof TypeError && error.message.includes('Failed to fetch')) {
        errorMessage = 'Network error - check if dashboard is running';
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }
      
      return { success: false, error: errorMessage };
    }
  }

  private async startInitialScraping() {
    try {
      const settings = this.apiClient.getSettings();
      if (!settings) {
        return { success: false, error: 'No settings found' };
      }

      // Get user profile info from API
      const profileInfo = await this.apiClient.getUserProfile();
      if (!profileInfo?.instagramUsername) {
        return { success: false, error: 'Instagram username not configured in dashboard' };
      }

      console.log('📱 Using Instagram username:', profileInfo.instagramUsername);

      // Always open new Instagram tab for clean state
      console.log('📱 Opening new Instagram tab...');
      const tab = await browser.tabs.create({ 
        url: 'https://www.instagram.com',
        active: false // Keep in background
      });
      const tabId = tab.id!;

      // Wait for page to load
      await DelayManager.wait(5000);
      console.log('✅ Instagram tab loaded');

      // Wait for content script to be ready
      console.log('⏳ Waiting for content script to be ready...');
      await this.waitForContentScript(tabId);
      
      // Step 1: Navigate to profile
      console.log('📍 Step 1: Navigating to profile...');
      const profileUrl = `https://instagram.com/${profileInfo.instagramUsername}`;
      await browser.tabs.update(tabId, { 
        url: profileUrl,
        active: false
      });
      
      // Wait for navigation to complete
      console.log('⏳ Waiting for profile page to load...');
      await DelayManager.wait(5000);
      
      // Wait for content script to be ready again after navigation
      console.log('⏳ Waiting for content script after navigation...');
      await this.waitForContentScript(tabId);
      
      // Step 2: Scrape followers using API (no modal needed!)
      console.log('🚀 Step 2: Starting API-based follower scraping...')
      
      const scrapeResponse = await this.sendMessageWithRetry(tabId, {
        action: 'SCRAPE_FOLLOWERS',
        count: 5000,
        username: profileInfo.instagramUsername
      }, 'Scrape Followers');
      
      console.log('📋 Scraping response:', scrapeResponse);
      
      if (!scrapeResponse || !(scrapeResponse as any).success) {
        const errorMsg = (scrapeResponse as any)?.error || 'Failed to scrape followers';
        console.error('❌ Scraping failed:', errorMsg);
        throw new Error(errorMsg);
      }
      
      // Upload followers to API
      const followers = (scrapeResponse as any).followers || [];
      console.log(`📤 Uploading ${followers.length} followers to API...`);
      
      if (followers.length > 0) {
        await this.uploadFollowersInBatches(followers);
        
        // Update scraping status in database via API
        console.log('📝 Updating scraping status to SCRAPING_COMPLETED in database...');
        await this.apiClient.updateScrapingStatus('SCRAPING_COMPLETED');
        
        // Also update local state
        this.state.scrapingStatus = 'SCRAPING_COMPLETED';
        await this.saveState();
        
        console.log('🎉 Initial scraping completed and uploaded to API!');
        console.log('✅ Database updated: INITIAL_SCRAPING → SCRAPING_COMPLETED');
      }
      
      console.log(`✅ Initial scraping workflow completed successfully`);
      return { success: true, followersCount: followers.length };
    } catch (error) {
      console.error('❌ Error in initial scraping:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  private async uploadFollowersInBatches(followers: FollowerData[]): Promise<void> {
    const batchSize = 50;
    const totalBatches = Math.ceil(followers.length / batchSize);
    
    console.log(`📤 Uploading ${followers.length} followers in ${totalBatches} batches of ${batchSize}...`);
    
    for (let i = 0; i < followers.length; i += batchSize) {
      const batch = followers.slice(i, i + batchSize);
      const batchNumber = Math.floor(i / batchSize) + 1;
      
      try {
        console.log(`📤 Uploading batch ${batchNumber}/${totalBatches} (${batch.length} followers)...`);
        
        await this.apiClient.uploadFollowers(batch, false); // false = not complete yet
        
        console.log(`✅ Batch ${batchNumber}/${totalBatches} uploaded successfully`);
        
        // Small delay between batches to avoid overwhelming the API
        if (i + batchSize < followers.length) {
          await DelayManager.wait(1000); // 1 second delay between batches
        }
        
      } catch (error) {
        console.error(`❌ Failed to upload batch ${batchNumber}:`, error);
        throw error; // Stop if any batch fails
      }
    }
    
    console.log('🎉 All follower batches uploaded successfully!');
  }

  private updateScrapingProgress(data: { current: number; total: number }) {
    if (this.state.scrapingProgress) {
      this.state.scrapingProgress.current = data.current;
      this.state.scrapingProgress.target = data.total;
      this.saveState();
    }
  }

  private async completeInitialScraping() {
    try {
      this.state.scrapingStatus = 'SCRAPING_COMPLETED';
      this.state.scrapingProgress!.isInitialScraping = false;
      await this.saveState();
      
      console.log('Initial scraping completed! Ready for normal automation.');
      return { success: true };
    } catch (error) {
      console.error('Error completing initial scraping:', error);
      return { success: false, error: error.message };
    }
  }

  // Catch-up scraping to find any followers missed while extension was stopped
  private async doCatchUpScraping(): Promise<{ success: boolean; newFollowersFound: number; error?: string }> {
    // Acquire processing lock to prevent main loop interference
    if (!this.stateManager.startProcessing()) {
      console.log('⚠️ Cannot start catch-up scraping - another process is running');
      return { success: false, newFollowersFound: 0, error: 'Another process is running' };
    }

    try {
      console.log('🔍 Starting catch-up scraping to find missed followers...');
      
      // Get list of recent follower usernames from database (last 100 for performance)
      console.log('📋 Fetching recent follower usernames from database...');
      const recentFollowerUsernames = await this.apiClient.getRecentFollowerUsernames();
      console.log(`📊 Checking against ${recentFollowerUsernames.length} recent follower usernames from database`);
      console.log('🔍 Sample database usernames:', recentFollowerUsernames.slice(0, 5));
      
      // Get Instagram username from API (like initial scraping does)
      const profileInfo = await this.apiClient.getUserProfile();
      if (!profileInfo?.instagramUsername) {
        throw new Error('Instagram username not configured in dashboard');
      }
      console.log('📱 Using Instagram username:', profileInfo.instagramUsername);
      
      // Open Instagram and navigate to user profile
      let tabs = await browser.tabs.query({ url: 'https://www.instagram.com/*' });
      let tab = tabs[0];
      
      if (!tab) {
        console.log('📱 Opening Instagram tab for catch-up scraping...');
        tab = await browser.tabs.create({
          url: `https://www.instagram.com/${profileInfo.instagramUsername}/`,
          active: false
        });
        await this.waitForTabLoad(tab.id!);
        await DelayManager.wait(5000); // Wait for login/content script
      } else {
        // Navigate existing tab to user profile
        console.log('📱 Navigating to user profile for catch-up scraping...');
        await browser.tabs.update(tab.id!, { 
          url: `https://www.instagram.com/${profileInfo.instagramUsername}/`,
          active: false 
        });
        await this.waitForTabLoad(tab.id!);
        await DelayManager.wait(3000);
      }

      // Ensure content script is ready
      await this.waitForContentScript(tab.id!);
      console.log('✅ Content script ready for catch-up scraping');

      // Start scraping followers
      let totalNewFollowers = 0;
      let shouldStopCatchUp = false;
      let batch = 1;
      let currentMaxId: string | undefined = undefined; // Track pagination across batches
      
      console.log('🚀 Starting API-based catch-up scraping (no modal needed)...');

      // Keep scraping until we get 3+ hits in a batch (hit-based stopping)
      while (!shouldStopCatchUp) {
        console.log(`🔍 Catch-up scraping batch ${batch} (pagination: ${currentMaxId ? 'continuing' : 'from start'})...`);
        
        const scrapeResult = await this.sendMessageWithRetry(
          tab.id!,
          { 
            action: 'SCRAPE_FOLLOWERS',
            count: 100, // Smaller batches for catch-up scraping 
            batchNumber: batch,
            username: profileInfo.instagramUsername,
            startMaxId: currentMaxId // Continue pagination from previous batch
          },
          `Scrape Batch ${batch}`,
          2 // Reduce retries for catch-up scraping to avoid conflicts
        );

        if (!scrapeResult || !(scrapeResult as any).success) {
          console.log(`⚠️ Scraping batch ${batch} failed during catch-up, stopping catch-up (this is normal)`);
          break;
        }

        const followers = (scrapeResult as any).followers || [];
        const nextMaxId = (scrapeResult as any).nextMaxId; // Get pagination state for next batch
        console.log(`📊 Batch ${batch}: Found ${followers.length} followers`);

        if (followers.length === 0) {
          console.log('📭 No more followers to scrape, catch-up complete');
          break;
        }

        // Check each follower against our recent followers from database
        const newFollowersInBatch: FollowerData[] = [];
        let hitsInThisBatch = 0;
        
        console.log('🔍 Sample scraped followers:', followers.slice(0, 3).map(f => ({ nickname: f.nickname, id: f.instagramUserId })));
        
        for (const follower of followers) {
          if (recentFollowerUsernames.includes(follower.nickname)) {
            hitsInThisBatch++;
            console.log(`✅ Hit ${hitsInThisBatch}: Found existing follower @${follower.nickname}`);
          } else {
            newFollowersInBatch.push(follower);
          }
        }

        console.log(`📊 Batch ${batch} results: ${hitsInThisBatch} hits, ${newFollowersInBatch.length} new followers`);

        // Upload only the new followers from this batch
        if (newFollowersInBatch.length > 0) {
          console.log(`📤 Uploading ${newFollowersInBatch.length} new followers from batch ${batch}`);
          await this.apiClient.uploadFollowers(newFollowersInBatch, false);
          totalNewFollowers += newFollowersInBatch.length;
        } else {
          console.log(`📭 No new followers in batch ${batch}`);
        }

        // Check if we should stop (3+ hits in this batch)
        if (hitsInThisBatch >= 3) {
          console.log(`🎯 Found ${hitsInThisBatch} hits in batch ${batch} (≥3), stopping catch-up scraping!`);
          shouldStopCatchUp = true;
        } else {
          console.log(`⏳ Only ${hitsInThisBatch} hits in batch ${batch} (<3), continuing to next batch...`);
          currentMaxId = nextMaxId; // Update pagination for next batch
          
          // Add delay between batches
          await DelayManager.wait(3000);
        }
        
        batch++;
      }

      console.log(`✅ Catch-up scraping completed! Processed ${batch - 1} batches`);
      return { 
        success: true, 
        newFollowersFound: totalNewFollowers 
      };

    } catch (error) {
      console.error('❌ Error in catch-up scraping:', error);
      return { 
        success: false, 
        newFollowersFound: 0,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    } finally {
      // Always release the processing lock
      this.stateManager.stopProcessing();
      console.log('🔓 Released processing lock from catch-up scraping');
    }
  }

  private async uploadFollowerBatch(data: { followers: FollowerData[]; isInitialScrapingComplete: boolean }) {
    try {
      const { followers, isInitialScrapingComplete } = data;
      
      console.log(`📤 Uploading ${followers.length} followers to API. Complete: ${isInitialScrapingComplete}`);
      
      await this.apiClient.uploadFollowers(followers, isInitialScrapingComplete);
      
      console.log('✅ Followers uploaded successfully');
      return { success: true };
    } catch (error) {
      console.error('❌ Error uploading follower batch:', error);
      return { success: false, error: error.message };
    }
  }

  // Check for new followers - called every 30 seconds
  private async checkForNewFollowers(): Promise<void> {
    try {
      const timestamp = new Date().toLocaleTimeString();
      console.log(`🔍 [${timestamp}] BACKGROUND: Starting follower check...`);

      // Get active Instagram tab
      const tabs = await browser.tabs.query({ url: ['*://www.instagram.com/*'] });
      if (!tabs.length) {
        console.log(`❌ [${timestamp}] BACKGROUND: No Instagram tab found for follower check`);
        return;
      }

      const activeTab = tabs[0];
      if (!activeTab.id) {
        console.log(`❌ [${timestamp}] BACKGROUND: Invalid tab ID for follower check`);
        return;
      }

      // Call content script to check for new followers
      console.log(`📨 [${timestamp}] BACKGROUND: Sending CHECK_NEW_FOLLOWERS to content script...`);
      const result = await browser.tabs.sendMessage(activeTab.id, {
        action: 'CHECK_NEW_FOLLOWERS'
      });

      if (result && result.success) {
        const followers = result.followers || [];
        console.log(`✅ [${timestamp}] BACKGROUND: Found ${followers.length} new followers`);
        
        if (followers.length > 0) {
          // Upload new followers to API
          console.log(`📤 [${timestamp}] BACKGROUND: Uploading ${followers.length} new followers...`);
          await this.apiClient.uploadFollowers(followers);
        }
      } else {
        console.log(`⚠️ [${timestamp}] BACKGROUND: Follower check failed:`, result?.error || 'Unknown error');
      }

    } catch (error) {
      const timestamp = new Date().toLocaleTimeString();
      console.error(`❌ [${timestamp}] BACKGROUND: Error in follower check:`, error);
    }
  }

  private async stopMainLoop() {
    console.log('🛑 Stopping main loop and monitoring...');
    
    // Set extension as not running first
    this.state.isRunning = false;
    
    // Clear main loop intervals
    if (this.mainLoopInterval) {
      console.log('🔄 Clearing main loop interval...');
      clearInterval(this.mainLoopInterval);
      this.mainLoopInterval = null;
    }
    
    if (this.monitoringInterval) {
      console.log('📊 Clearing monitoring interval...');
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }
    
    // Clear all periodic task intervals
    this.clearPeriodicTasks();
    
    // Clear any processing locks
    if (this.stateManager.isCurrentlyProcessing()) {
      console.log('🔓 Clearing processing lock...');
      this.stateManager.stopProcessing();
    }
    
    // Close Instagram tabs
    try {
      const instagramTabs = await browser.tabs.query({ url: 'https://www.instagram.com/*' });
      console.log(`📱 Found ${instagramTabs.length} Instagram tabs to close`);
      
      for (const tab of instagramTabs) {
        if (tab.id) {
          await browser.tabs.remove(tab.id);
          console.log(`✅ Closed Instagram tab: ${tab.id}`);
        }
      }
    } catch (error) {
      console.error('❌ Error closing Instagram tabs:', error);
    }
    
    // Save final state
    await this.saveState();
    
    console.log('✅ Complete extension cleanup completed');
  }
}