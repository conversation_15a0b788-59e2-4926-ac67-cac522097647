export class ActionScheduler {
  private scheduledActions: Map<string, any> = new Map();

  scheduleAction(id: string, callback: () => void, delay: number) {
    // Clear existing action if scheduled
    this.cancelAction(id);
    
    const timeout = setTimeout(() => {
      callback();
      this.scheduledActions.delete(id);
    }, delay);
    
    this.scheduledActions.set(id, timeout);
  }

  cancelAction(id: string) {
    const timeout = this.scheduledActions.get(id);
    if (timeout) {
      clearTimeout(timeout);
      this.scheduledActions.delete(id);
    }
  }

  cancelAllActions() {
    for (const timeout of this.scheduledActions.values()) {
      clearTimeout(timeout);
    }
    this.scheduledActions.clear();
  }
}