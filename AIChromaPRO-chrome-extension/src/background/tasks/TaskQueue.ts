// Simple Task Interface
export interface Task {
  name: string;
  execute(): Promise<void>;
}

// Simple TaskQueue - replaces all intervals and complex loops
export class TaskQueue {
  private queue: Task[] = [];
  private isProcessing = false;
  private isRunning = false;

  add(task: Task): void {
    this.queue.push(task);
    console.log(`📋 Added task: ${task.name} (Queue: ${this.queue.length})`);
  }

  start(): void {
    if (this.isRunning) return;
    this.isRunning = true;
    console.log('🔄 TaskQueue started');
    this.processQueue();
  }

  stop(): void {
    this.isRunning = false;
    console.log('🛑 TaskQueue stopped');
  }

  clear(): void {
    this.queue = [];
    console.log('🗑️ TaskQueue cleared');
  }

  private async processQueue(): Promise<void> {
    while (this.isRunning) {
      if (this.queue.length === 0) {
        // No tasks, wait a bit and check again
        await new Promise(resolve => setTimeout(resolve, 1000));
        continue;
      }

      if (this.isProcessing) {
        // Already processing a task, wait
        await new Promise(resolve => setTimeout(resolve, 100));
        continue;
      }

      // Get next task
      const task = this.queue.shift();
      if (!task) continue;

      this.isProcessing = true;
      console.log(`▶️ Executing task: ${task.name}`);

      try {
        await task.execute();
        console.log(`✅ Completed task: ${task.name}`);
      } catch (error) {
        console.error(`❌ Task failed: ${task.name}`, error);
      }

      this.isProcessing = false;
    }
  }

  getQueueSize(): number {
    return this.queue.length;
  }

  isCurrentlyProcessing(): boolean {
    return this.isProcessing;
  }
}