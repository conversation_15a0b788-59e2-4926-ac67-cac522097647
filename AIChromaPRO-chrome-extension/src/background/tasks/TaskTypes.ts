import { Task } from './TaskQueue';
import { AttackQueueItem } from '../../types';
import { DelayManager } from '../managers/DelayManager';

// Forward declaration for BackgroundService
interface BackgroundServiceInterface {
  checkForNewFollowersTask(): Promise<void>;
  sendSingleMessage(message: AttackQueueItem, messageText: string, messageIndex: number, totalMessages: number): Promise<void>;
  navigateToProfileTask(username: string): Promise<void>;
  clickMessageButtonTask(username: string): Promise<void>;
}

// Basic Task Types
export class WaitTask implements Task {
  name: string;
  
  constructor(private duration: number, private description: string) {
    this.name = `Wait ${Math.round(duration/1000)}s - ${description}`;
  }

  async execute(): Promise<void> {
    await DelayManager.wait(this.duration);
  }
}

export class CheckFollowersTask implements Task {
  name = "Check for new followers";
  
  constructor(private backgroundService: BackgroundServiceInterface) {}

  async execute(): Promise<void> {
    await this.backgroundService.checkForNewFollowersTask();
  }
}

export class SendMessageTask implements Task {
  name: string;
  
  constructor(
    private backgroundService: BackgroundServiceInterface,
    private message: AttackQueueItem,
    private messageText: string,
    private messageIndex: number,
    private totalMessages: number
  ) {
    this.name = `Send message ${messageIndex}/${totalMessages} to @${message.nickname}`;
  }

  async execute(): Promise<void> {
    await this.backgroundService.sendSingleMessage(this.message, this.messageText, this.messageIndex, this.totalMessages);
  }
}

export class NavigateToProfileTask implements Task {
  name: string;
  
  constructor(private backgroundService: BackgroundServiceInterface, private username: string) {
    this.name = `Navigate to profile @${username}`;
  }

  async execute(): Promise<void> {
    await this.backgroundService.navigateToProfileTask(this.username);
  }
}

export class ClickMessageButtonTask implements Task {
  name: string;
  
  constructor(private backgroundService: BackgroundServiceInterface, private username: string) {
    this.name = `Click message button for @${username}`;
  }

  async execute(): Promise<void> {
    await this.backgroundService.clickMessageButtonTask(this.username);
  }
}